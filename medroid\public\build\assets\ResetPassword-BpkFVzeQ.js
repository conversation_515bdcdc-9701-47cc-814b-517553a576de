import{N as _,V as g,y as f,e as u,g as t,f as a,i as l,u as s,m as V,j as b,x as i,l as k}from"./vendor-DwpQ5WHX.js";import{_ as m}from"./InputError.vue_vue_type_script_setup_true_lang-DSYw3Bxn.js";import{_ as y}from"./index-CDjkLHA6.js";import{_ as d,a as n}from"./Label.vue_vue_type_script_setup_true_lang-L1W7djSO.js";import{L as C,_ as v}from"./AuthLayout.vue_vue_type_script_setup_true_lang-CDXnnkHu.js";import"./Primitive-lW_nm-2e.js";import"./index-0ZTvHY_i.js";import"./createLucideIcon-Di250PjL.js";const x={class:"grid gap-6"},N={class:"grid gap-2"},P={class:"grid gap-2"},$={class:"grid gap-2"},T=_({__name:"ResetPassword",props:{token:{},email:{}},setup(w){const p=w,e=g({token:p.token,email:p.email,password:"",password_confirmation:""}),c=()=>{e.post(route("password.store"),{onFinish:()=>{e.reset("password","password_confirmation")}})};return(R,o)=>(u(),f(v,{title:"Reset password",description:"Please enter your new password below"},{default:t(()=>[a(s(V),{title:"Reset password"}),l("form",{onSubmit:b(c,["prevent"])},[l("div",x,[l("div",N,[a(s(d),{for:"email"},{default:t(()=>o[3]||(o[3]=[i("Email")])),_:1}),a(s(n),{id:"email",type:"email",name:"email",autocomplete:"email",modelValue:s(e).email,"onUpdate:modelValue":o[0]||(o[0]=r=>s(e).email=r),class:"mt-1 block w-full",readonly:""},null,8,["modelValue"]),a(m,{message:s(e).errors.email,class:"mt-2"},null,8,["message"])]),l("div",P,[a(s(d),{for:"password"},{default:t(()=>o[4]||(o[4]=[i("Password")])),_:1}),a(s(n),{id:"password",type:"password",name:"password",autocomplete:"new-password",modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=r=>s(e).password=r),class:"mt-1 block w-full",autofocus:"",placeholder:"Password"},null,8,["modelValue"]),a(m,{message:s(e).errors.password},null,8,["message"])]),l("div",$,[a(s(d),{for:"password_confirmation"},{default:t(()=>o[5]||(o[5]=[i(" Confirm Password ")])),_:1}),a(s(n),{id:"password_confirmation",type:"password",name:"password_confirmation",autocomplete:"new-password",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=r=>s(e).password_confirmation=r),class:"mt-1 block w-full",placeholder:"Confirm password"},null,8,["modelValue"]),a(m,{message:s(e).errors.password_confirmation},null,8,["message"])]),a(s(y),{type:"submit",class:"mt-4 w-full",disabled:s(e).processing},{default:t(()=>[s(e).processing?(u(),f(s(C),{key:0,class:"h-4 w-4 animate-spin"})):k("",!0),o[6]||(o[6]=i(" Reset password "))]),_:1},8,["disabled"])])],32)]),_:1}))}});export{T as default};
