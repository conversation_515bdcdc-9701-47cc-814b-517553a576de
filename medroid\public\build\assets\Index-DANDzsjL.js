import{z as R,c as g,r as x,o as q,d as l,e as r,f as w,u as y,m as K,g as p,i as t,n as P,v as Q,F as f,p as C,t as d,q as N,A as T,x as m,l as v,y as S,P as h}from"./vendor-DwpQ5WHX.js";import{_ as G}from"./AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-lW_nm-2e.js";import"./createLucideIcon-Di250PjL.js";const H={class:"flex items-center justify-between"},J={class:"flex mt-2","aria-label":"Breadcrumb"},O={class:"inline-flex items-center space-x-1 md:space-x-3"},W={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},X={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},Y={class:"py-12"},Z={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},ee={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},te={class:"p-6"},se={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ae=["value"],re={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},le={class:"p-6 text-gray-900 dark:text-gray-100"},de={key:0,class:"text-center py-8"},oe={key:1,class:"text-center py-8"},ne={key:2,class:"overflow-x-auto"},ie={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ue={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ce={class:"px-6 py-4 whitespace-nowrap"},ge={class:"flex items-center"},xe={class:"ml-4"},pe={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ye={class:"text-sm text-gray-500 dark:text-gray-400"},me={class:"px-6 py-4 whitespace-nowrap"},ve={class:"text-sm text-gray-900 dark:text-gray-100"},fe={class:"px-6 py-4 whitespace-nowrap"},he={class:"px-6 py-4 whitespace-nowrap"},be={class:"text-sm text-gray-900 dark:text-gray-100"},ke={key:0,class:"text-xs text-gray-500 line-through ml-1"},_e={class:"px-6 py-4 whitespace-nowrap"},we={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Pe={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Le={__name:"Index",setup(Ce){const U=R(),i=g(()=>{var a;return(a=U.props.auth)==null?void 0:a.user}),b=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"}],k=x(!1),_=x([]),A=x([]),n=x(""),u=x("all"),c=x("all"),B=async()=>{var a;k.value=!0;try{const e=new URLSearchParams;n.value&&e.append("search",n.value),u.value!=="all"&&e.append("category",u.value),c.value!=="all"&&e.append("type",c.value);const s=await window.axios.get(`/admin/products-list?${e.toString()}`);_.value=((a=s.data.products)==null?void 0:a.data)||s.data.products||[],s.data.categories&&(A.value=s.data.categories)}catch(e){console.error("Error fetching products:",e),_.value=[]}finally{k.value=!1}},M=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",E=a=>a==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",D=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),V=g(()=>_.value.filter(a=>{const e=!n.value||a.name.toLowerCase().includes(n.value.toLowerCase())||a.sku.toLowerCase().includes(n.value.toLowerCase()),s=u.value==="all"||a.category_id==u.value,o=c.value==="all"||a.type===c.value;return e&&s&&o})),L=g(()=>{var a,e;return((e=(a=i.value)==null?void 0:a.roles)==null?void 0:e.some(s=>s.name==="admin"))||!1}),$=g(()=>{var a,e;return((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("create products"))||!1}),z=g(()=>{var a,e;return((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("edit products"))||!1}),F=g(()=>{var a,e;return((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("delete products"))||!1}),I=a=>{var e;return L.value?!0:z.value&&a.user_id===((e=i.value)==null?void 0:e.id)},j=a=>{var e;return L.value?!0:F.value&&a.user_id===((e=i.value)==null?void 0:e.id)};return q(()=>{B()}),(a,e)=>(r(),l(f,null,[w(y(K),{title:"Product Management"}),w(G,null,{header:p(()=>[t("div",H,[t("div",null,[e[4]||(e[4]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Product Management ",-1)),t("nav",J,[t("ol",O,[(r(),l(f,null,C(b,(s,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<b.length-1?(r(),S(y(h),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:p(()=>[m(d(s.title),1)]),_:2},1032,["href"])):(r(),l("span",W,d(s.title),1)),o<b.length-1?(r(),l("svg",X,e[3]||(e[3]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),$.value?(r(),S(y(h),{key:0,href:"/admin/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:p(()=>e[5]||(e[5]=[m(" Add Product ")])),_:1})):v("",!0)])]),default:p(()=>[t("div",Y,[t("div",Z,[t("div",ee,[t("div",te,[t("div",se,[t("div",null,[P(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>n.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[Q,n.value]])]),t("div",null,[P(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[6]||(e[6]=t("option",{value:"all"},"All Categories",-1)),(r(!0),l(f,null,C(A.value,s=>(r(),l("option",{key:s.id,value:s.id},d(s.name),9,ae))),128))],512),[[N,u.value]])]),t("div",null,[P(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>c.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[7]||(e[7]=[t("option",{value:"all"},"All Types",-1),t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[N,c.value]])]),t("div",null,[t("button",{onClick:B,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),t("div",re,[t("div",le,[k.value?(r(),l("div",de,e[8]||(e[8]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):V.value.length===0?(r(),l("div",oe,e[9]||(e[9]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(r(),l("div",ne,[t("table",ie,[e[13]||(e[13]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",ue,[(r(!0),l(f,null,C(V.value,s=>{var o;return r(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ce,[t("div",ge,[e[10]||(e[10]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[t("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"})])],-1)),t("div",xe,[t("div",pe,d(s.name),1),t("div",ye," SKU: "+d(s.sku),1)])])]),t("td",me,[t("span",ve,d(((o=s.category)==null?void 0:o.name)||"N/A"),1)]),t("td",fe,[t("span",{class:T([E(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.type),3)]),t("td",he,[t("div",be,[m(d(D(s.price))+" ",1),s.sale_price?(r(),l("span",ke,d(D(s.sale_price)),1)):v("",!0)])]),t("td",_e,[t("span",{class:T([M(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},d(s.is_active?"Active":"Inactive"),3)]),t("td",we,[w(y(h),{href:`/admin/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:p(()=>e[11]||(e[11]=[m(" View ")])),_:2},1032,["href"]),I(s)?(r(),S(y(h),{key:0,href:`/admin/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:p(()=>e[12]||(e[12]=[m(" Edit ")])),_:2},1032,["href"])):v("",!0),j(s)?(r(),l("button",Pe," Delete ")):v("",!0)])])}),128))])])]))])])])])]),_:1})],64))}};export{Le as default};
