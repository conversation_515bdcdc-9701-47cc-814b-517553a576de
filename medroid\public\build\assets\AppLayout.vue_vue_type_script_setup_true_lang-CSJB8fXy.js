import{r as A,c as l,d as i,e as a,J as L,A as b,t as x,W as is,o as ns,H as ls,i as r,f as _,l as y,F as B,p as P,g as v,u as m,P as w,x as C,y as k,a as T,N as z,Q as $,z as us}from"./vendor-DwpQ5WHX.js";import{_ as Ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as H,P as cs}from"./Primitive-lW_nm-2e.js";import{c as ds}from"./createLucideIcon-Di250PjL.js";const ms=["src","alt"],ps={__name:"MedroidLogo",props:{size:{type:Number,default:32},color:{type:String,default:null},useDarkVersion:{type:Boolean,default:!1},showShadow:{type:Boolean,default:!0},showIcon:{type:<PERSON>olean,default:!0},iconClass:{type:String,default:"fas fa-heart"},fallbackText:{type:String,default:"M"},alt:{type:String,default:"Medroid Logo"}},setup(u){const t=u,o=A(!1),d=A(!1),g=l(()=>"/medroid_logo.png"),f=l(()=>({display:"inline-block",width:`${t.size}px`,height:`${t.size}px`})),F=l(()=>({width:"100%",height:"100%",objectFit:"contain",display:"block"})),E=l(()=>({width:"100%",height:"100%",borderRadius:"50%",background:`linear-gradient(135deg, ${t.color||"#17C3B2"} 0%, #8BE9C8 100%)`,display:"flex",alignItems:"center",justifyContent:"center",boxShadow:t.showShadow?`0 ${t.size*.1}px ${t.size*.15}px rgba(23, 195, 178, 0.3)`:"none"})),R=l(()=>({fontSize:`${t.size*.5}px`,color:"white"})),n=l(()=>({fontSize:`${t.size*.6}px`,fontWeight:"bold",color:"white",fontFamily:"Arial, sans-serif"})),j=()=>{o.value=!0},V=()=>{d.value=!0,o.value=!1};return(N,I)=>(a(),i("div",{class:"medroid-logo",style:L(f.value)},[o.value?(a(),i("div",{key:1,class:"logo-fallback",style:L(E.value)},[u.showIcon?(a(),i("i",{key:0,class:b(u.iconClass),style:L(R.value)},null,6)):(a(),i("span",{key:1,class:"logo-text",style:L(n.value)},x(u.fallbackText),5))],4)):(a(),i("img",{key:0,src:g.value,alt:u.alt,style:L(F.value),onError:j,onLoad:V},null,44,ms))],4))}},hs=Ee(ps,[["__scopeId","data-v-015d9d74"]]);function vs(){return{logout:()=>{try{localStorage.removeItem("user"),localStorage.removeItem("auth_token"),localStorage.removeItem("chat_history"),sessionStorage.clear()}catch(t){console.log("Error clearing storage:",t)}is.post("/logout",{},{onFinish:()=>{window.location.href="/"}})}}}const fs={class:"h-full bg-white border-r border-gray-200 text-gray-800 flex flex-col"},gs={class:"flex items-center justify-between p-3 border-b border-gray-200"},_s={class:"flex items-center"},ys={key:0,class:"ml-2 text-lg font-semibold text-medroid-navy"},xs={class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},bs={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},ws={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"},ks={class:"flex-1 overflow-y-auto py-3"},Ms={class:"space-y-1 px-2 flex flex-col h-full"},Cs={class:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},zs=["d"],$s={key:0,class:"ml-2"},Vs={key:0,class:"pt-6 flex flex-col flex-1 min-h-0"},Ss={class:"px-3 mb-2"},As={class:"flex items-center justify-between"},Ls={class:"flex-1 min-h-0 overflow-y-auto"},Bs={key:0,class:"px-3 py-2"},Hs={key:1,class:"space-y-1 px-3"},js={class:"flex-1 min-w-0"},Is={class:"text-gray-900 font-medium truncate group-hover:text-medroid-orange"},Ps={class:"text-gray-500 text-xs mt-0.5"},Fs={key:2,class:"px-3 py-2"},Es={key:0,class:"px-3 py-2 border-t border-gray-200 mt-auto"},Rs={class:"text-xs font-semibold text-green-600 hover:text-medroid-orange"},Ns={key:1,class:"pt-6"},Ts={key:0,class:"px-3 mb-2"},Ws={class:"w-5 h-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ds=["d"],qs={key:0,class:"ml-3"},Us={class:"border-t border-gray-200 p-3"},Os={class:"flex items-center"},Xs={key:0,class:"ml-2 flex-1"},Js={class:"flex items-center gap-2"},Gs={class:"text-sm font-medium text-gray-900"},Ks=["title"],Qs={class:"text-xs text-gray-500 capitalize"},Ys={key:0,class:"mt-3 space-y-1"},Zs={key:1,class:"mt-3"},et={__name:"AppSidebar",props:{user:{type:Object,required:!1,default:()=>null}},setup(u){const t=u,o=A(!1),d=A([]),g=A(!1),f=A({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),F=l(()=>{var s;return((s=t.user)==null?void 0:s.role)||"patient"}),E=l(()=>{var s;return((s=t.user)==null?void 0:s.is_founder_member)||!1}),R=l(()=>{var s;return((s=t.user)==null?void 0:s.founder_club_info)||null}),n=l(()=>{var s,e;return((e=(s=t.user)==null?void 0:s.roles)==null?void 0:e.some(p=>p.name==="admin"))||!1}),j=l(()=>{var s,e;return((e=(s=t.user)==null?void 0:s.roles)==null?void 0:e.some(p=>p.name==="provider"))||!1}),V=l(()=>{var s,e;return((e=(s=t.user)==null?void 0:s.roles)==null?void 0:e.some(p=>p.name==="patient"))||!1}),N=l(()=>{const s=[];return(n.value||j.value)&&s.push({title:"Dashboard",href:"/dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",permission:!0}),V.value&&s.push({title:"Chat",href:"/chat",icon:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",permission:!0},{title:"Discover",href:"/discover",icon:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",permission:!0},{title:"Shop",href:"/shop",icon:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z",permission:!0},{title:"Appointments",href:"/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0}),j.value&&s.push({title:"Schedule",href:"/provider/schedule",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0},{title:"Availability",href:"/provider/availability",icon:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0},{title:"Appointments",href:"/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z",permission:!0},{title:"My Patients",href:"/provider/patients",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:!0},{title:"Services",href:"/provider/services",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",permission:!0},{title:"Earnings",href:"/provider/earnings",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0}),s}),I=l(()=>{var e,p,h,c,M,S,q,U,O,X,J,G,K,Q,Y,Z,ee,se,te,re,ae,oe,ie,ne,le,ue,ce,de,me,pe,he,ve,fe,ge,_e,ye,xe,be,we,ke,Me,Ce,ze,$e,Ve,Se,Ae,Le,Be,He,je,Ie,Pe,Fe;return n.value||((p=(e=t.user)==null?void 0:e.user_permissions)==null?void 0:p.includes("view users"))||((c=(h=t.user)==null?void 0:h.user_permissions)==null?void 0:c.includes("view providers"))||((S=(M=t.user)==null?void 0:M.user_permissions)==null?void 0:S.includes("view patients"))||((U=(q=t.user)==null?void 0:q.user_permissions)==null?void 0:U.includes("view clinics"))||((X=(O=t.user)==null?void 0:O.user_permissions)==null?void 0:X.includes("view appointments"))||((G=(J=t.user)==null?void 0:J.user_permissions)==null?void 0:G.includes("view payments"))||((Q=(K=t.user)==null?void 0:K.user_permissions)==null?void 0:Q.includes("view chats"))||((Z=(Y=t.user)==null?void 0:Y.user_permissions)==null?void 0:Z.includes("manage services"))||((se=(ee=t.user)==null?void 0:ee.user_permissions)==null?void 0:se.includes("view products"))||((re=(te=t.user)==null?void 0:te.user_permissions)==null?void 0:re.includes("view orders"))?[{title:"Users",href:"/users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:n.value||((oe=(ae=t.user)==null?void 0:ae.user_permissions)==null?void 0:oe.includes("view users"))},{title:"Providers",href:"/providers",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",permission:n.value||((ne=(ie=t.user)==null?void 0:ie.user_permissions)==null?void 0:ne.includes("view providers"))},{title:"Patients",href:"/patients",icon:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",permission:n.value||((ue=(le=t.user)==null?void 0:le.user_permissions)==null?void 0:ue.includes("view patients"))},{title:"Clinics",href:"/clinics",icon:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",permission:n.value||((de=(ce=t.user)==null?void 0:ce.user_permissions)==null?void 0:de.includes("view clinics"))},{title:"Appointments",href:"/manage/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:n.value||((pe=(me=t.user)==null?void 0:me.user_permissions)==null?void 0:pe.includes("view appointments"))},{title:"Payments",href:"/payments",icon:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z",permission:n.value||((ve=(he=t.user)==null?void 0:he.user_permissions)==null?void 0:ve.includes("view payments"))},{title:"Chats",href:"/chats",icon:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",permission:n.value||((ge=(fe=t.user)==null?void 0:fe.user_permissions)==null?void 0:ge.includes("view chats"))},{title:"Permissions",href:"/permissions",icon:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",permission:n.value||((ye=(_e=t.user)==null?void 0:_e.user_permissions)==null?void 0:ye.includes("manage permissions"))},{title:"Services",href:"/services",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",permission:n.value||((be=(xe=t.user)==null?void 0:xe.user_permissions)==null?void 0:be.includes("manage services"))},{title:"Products",href:"/admin/products",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6",permission:n.value||((ke=(we=t.user)==null?void 0:we.user_permissions)==null?void 0:ke.includes("view products"))},{title:"Orders",href:"/admin/orders",icon:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",permission:n.value||((Ce=(Me=t.user)==null?void 0:Me.user_permissions)==null?void 0:Ce.includes("view orders"))},{title:"Email Templates",href:"/email-templates",icon:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",permission:n.value||(($e=(ze=t.user)==null?void 0:ze.user_permissions)==null?void 0:$e.includes("view email templates"))},{title:"Notifications",href:"/notifications",icon:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9",permission:n.value||((Se=(Ve=t.user)==null?void 0:Ve.user_permissions)==null?void 0:Se.includes("view notifications"))},{title:"Referrals",href:"/referrals",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:n.value||((Le=(Ae=t.user)==null?void 0:Ae.user_permissions)==null?void 0:Le.includes("view referrals"))},{title:"Credits",href:"/credits",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",permission:n.value||((He=(Be=t.user)==null?void 0:Be.user_permissions)==null?void 0:He.includes("view credits"))},{title:"Clubs",href:"/clubs",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",permission:n.value||((Ie=(je=t.user)==null?void 0:je.user_permissions)==null?void 0:Ie.includes("view clubs"))},{title:"Waitlist",href:"/waitlist",icon:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",permission:n.value||((Fe=(Pe=t.user)==null?void 0:Pe.user_permissions)==null?void 0:Fe.includes("view users"))}].filter(os=>os.permission):[]}),Re=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view users"))}),Ne=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view providers"))}),Te=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view patients"))});l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view analytics"))});const We=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view appointments"))}),De=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view payments"))}),qe=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view chats"))}),Ue=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage notifications"))}),Oe=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage email templates"))}),Xe=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage permissions"))}),Je=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage services"))}),Ge=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view referrals"))}),Ke=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view credits"))}),Qe=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view clubs"))}),Ye=l(()=>{var s,e;return n.value||((e=(s=t.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view clinics"))});l(()=>Re.value||Ne.value||Te.value||Ye.value||We.value||De.value||qe.value||Je.value),l(()=>Xe.value||Oe.value||Ue.value||Ge.value||Ke.value||Qe.value);const Ze=()=>{o.value=!o.value,localStorage.setItem("app-sidebar-collapsed",o.value)},W=()=>{window.innerWidth<768&&!o.value&&(o.value=!0,localStorage.setItem("app-sidebar-collapsed","true"))},es=async()=>{if(V.value)try{const s=await T.get("/credits-balance");f.value=s.data}catch(s){console.error("Error loading credit balance:",s),f.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}}},ss=async()=>{var s;if(V.value){g.value=!0;try{try{await T.post("/web-api/chat/update-titles",{},{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})}catch(M){console.log("Title update failed (non-critical):",M)}const p=(await T.get("/web-api/chat/history")).data;let h=[];Array.isArray(p)?h=p:p.conversations&&Array.isArray(p.conversations)?h=p.conversations:p.data&&Array.isArray(p.data)&&(h=p.data),Array.isArray(h)||(console.warn("conversations is not an array in AppSidebar:",h),h=[]);const c=h.filter(M=>!M.messages||!Array.isArray(M.messages)?!1:M.messages.some(S=>S&&S.content&&S.content.trim().length>0));d.value=c.slice(0,8)}catch(e){console.error("Error fetching chat history:",e),d.value=[]}finally{g.value=!1}}},ts=s=>{if(s.title&&s.title.trim())return s.title.length>25?s.title.substring(0,25)+"...":s.title;if(s.messages&&s.messages.length>0){const e=s.messages[0].content;return e.length>25?e.substring(0,25)+"...":e}return"New Chat"},rs=s=>{const e=new Date(s),h=Math.floor((new Date-e)/(1e3*60*60));return h<1?"Just now":h<24?`${h}h ago`:h<168?`${Math.floor(h/24)}d ago`:e.toLocaleDateString()},{logout:as}=vs(),D=()=>{f.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0},d.value=[],as()};return ns(()=>{const s=localStorage.getItem("app-sidebar-collapsed");s!==null&&(o.value=s==="true"),window.innerWidth<768&&(o.value=!0),window.addEventListener("resize",W),V.value&&(ss(),es())}),ls(()=>{window.removeEventListener("resize",W)}),(s,e)=>{var p,h;return a(),i("div",{class:b(["h-screen flex-shrink-0",[o.value?"w-16":"w-64","transition-all duration-300"]])},[r("div",fs,[r("div",gs,[r("div",_s,[_(hs,{size:28}),o.value?y("",!0):(a(),i("span",ys,"Medroid"))]),r("button",{onClick:Ze,class:"text-gray-400 hover:text-gray-600 focus:outline-none"},[(a(),i("svg",xs,[o.value?(a(),i("path",bs)):(a(),i("path",ws))]))])]),r("div",ks,[r("nav",Ms,[(a(!0),i(B,null,P(N.value,c=>(a(),i("div",{key:c.href},[_(m(w),{href:c.href,class:b(["flex items-center px-2 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200",[s.$page.url===c.href||s.$page.url.startsWith(c.href+"/")?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:v(()=>[(a(),i("svg",Cs,[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c.icon},null,8,zs)])),o.value?y("",!0):(a(),i("span",$s,x(c.title),1))]),_:2},1032,["href","class"])]))),128)),V.value&&!o.value?(a(),i("div",Vs,[r("div",Ss,[r("div",As,[e[1]||(e[1]=r("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Recent Chats",-1)),_(m(w),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark",title:"Start new chat"},{default:v(()=>e[0]||(e[0]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1})])]),r("div",Ls,[g.value?(a(),i("div",Bs,e[2]||(e[2]=[r("div",{class:"flex items-center text-xs text-gray-500"},[r("div",{class:"animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400 mr-2"}),C(" Loading chats... ")],-1)]))):d.value.length>0?(a(),i("div",Hs,[(a(!0),i(B,null,P(d.value,c=>(a(),k(m(w),{key:c._id||c.id,href:`/chat?conversation=${c._id||c.id}`,class:"flex items-start py-2 text-xs rounded-lg transition-colors duration-200 hover:bg-gray-50 group block"},{default:v(()=>[e[3]||(e[3]=r("div",{class:"flex-shrink-0 mr-2 mt-0.5"},[r("div",{class:"w-2 h-2 bg-medroid-orange rounded-full"})],-1)),r("div",js,[r("p",Is,x(ts(c)),1),r("p",Ps,x(rs(c.updated_at||c.createdAt)),1)])]),_:2},1032,["href"]))),128))])):(a(),i("div",Fs,[e[5]||(e[5]=r("p",{class:"text-xs text-gray-500"},"No recent chats",-1)),_(m(w),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark mt-1 inline-block"},{default:v(()=>e[4]||(e[4]=[C(" Start your first chat ")])),_:1})]))]),d.value.length>0?(a(),i("div",Es,[_(m(w),{href:"/chat-history",class:"text-xs text-gray-600 hover:text-medroid-orange flex items-center justify-start py-1 transition-colors"},{default:v(()=>e[6]||(e[6]=[r("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1),C(" View all chats ")])),_:1})])):y("",!0),r("div",{class:b(["px-3 py-3 border-t border-gray-200",{"mt-auto":d.value.length===0}])},[_(m(w),{href:"/credit-history",class:"inline-flex items-center justify-between w-auto px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 transition-colors shadow-sm",title:`Total Balance: $${parseFloat(f.value.balance||0).toFixed(2)} | Referrals: $${parseFloat(f.value.referral_earnings||0).toFixed(2)} | Admin: $${parseFloat(f.value.admin_credits||0).toFixed(2)} | Used: $${parseFloat(f.value.total_used||0).toFixed(2)}`},{default:v(()=>[e[7]||(e[7]=r("div",{class:"flex items-center"},[r("svg",{class:"w-3 h-3 mr-1 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),r("span",{class:"text-xs text-gray-600 hover:text-medroid-orange mr-2"},"Credits:")],-1)),r("span",Rs,"$"+x(parseFloat(f.value.balance||0).toFixed(2)),1)]),_:1},8,["title"])],2)])):y("",!0),I.value.length>0?(a(),i("div",Ns,[o.value?y("",!0):(a(),i("div",Ts,e[8]||(e[8]=[r("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Management",-1)]))),(a(!0),i(B,null,P(I.value,c=>(a(),i("div",{key:c.href},[_(m(w),{href:c.href,class:b(["flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200",[s.$page.url===c.href||s.$page.url.startsWith(c.href+"/")?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:v(()=>[(a(),i("svg",Ws,[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c.icon},null,8,Ds)])),o.value?y("",!0):(a(),i("span",qs,x(c.title),1))]),_:2},1032,["href","class"])]))),128))])):y("",!0)])]),r("div",Us,[r("div",Os,[e[10]||(e[10]=r("div",{class:"w-7 h-7 bg-gray-300 rounded-full flex items-center justify-center"},[r("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),o.value?y("",!0):(a(),i("div",Xs,[r("div",Js,[r("p",Gs,x((p=u.user)==null?void 0:p.name),1),E.value?(a(),i("div",{key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-sm",title:((h=R.value)==null?void 0:h.club_name)||"Medroid Founders Club"},e[9]||(e[9]=[r("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[r("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1),C(" Founders' Club ")]),8,Ks)):y("",!0)]),r("p",Qs,x(F.value),1)]))]),o.value?(a(),i("div",Zs,[r("button",{onClick:D,class:"flex items-center justify-center p-2 text-red-600 hover:text-red-800 rounded",title:"Logout"},e[13]||(e[13]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1)]))])):(a(),i("div",Ys,[_(m(w),{href:"/settings/profile",class:"flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-900 rounded"},{default:v(()=>e[11]||(e[11]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),C(" Settings ")])),_:1}),r("button",{onClick:D,class:"flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 rounded w-full text-left"},e[12]||(e[12]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),C(" Logout ")]))]))])])],2)}}},st=Ee(et,[["__scopeId","data-v-4ee2a32f"]]),tt=z({__name:"Breadcrumb",props:{class:{}},setup(u){const t=u;return(o,d)=>(a(),i("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:b(t.class)},[$(o.$slots,"default")],2))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rt=ds("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),at=z({__name:"BreadcrumbItem",props:{class:{}},setup(u){const t=u;return(o,d)=>(a(),i("li",{"data-slot":"breadcrumb-item",class:b(m(H)("inline-flex items-center gap-1.5",t.class))},[$(o.$slots,"default")],2))}}),ot=z({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(u){const t=u;return(o,d)=>(a(),k(m(cs),{"data-slot":"breadcrumb-link",as:o.as,"as-child":o.asChild,class:b(m(H)("hover:text-foreground transition-colors",t.class))},{default:v(()=>[$(o.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),it=z({__name:"BreadcrumbList",props:{class:{}},setup(u){const t=u;return(o,d)=>(a(),i("ol",{"data-slot":"breadcrumb-list",class:b(m(H)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t.class))},[$(o.$slots,"default")],2))}}),nt=z({__name:"BreadcrumbPage",props:{class:{}},setup(u){const t=u;return(o,d)=>(a(),i("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:b(m(H)("text-foreground font-normal",t.class))},[$(o.$slots,"default")],2))}}),lt=z({__name:"BreadcrumbSeparator",props:{class:{}},setup(u){const t=u;return(o,d)=>(a(),i("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:b(m(H)("[&>svg]:size-3.5",t.class))},[$(o.$slots,"default",{},()=>[_(m(rt))])],2))}}),ut=z({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(u){return(t,o)=>(a(),k(m(tt),null,{default:v(()=>[_(m(it),null,{default:v(()=>[(a(!0),i(B,null,P(t.breadcrumbs,(d,g)=>(a(),i(B,{key:g},[_(m(at),null,{default:v(()=>[g===t.breadcrumbs.length-1?(a(),k(m(nt),{key:0},{default:v(()=>[C(x(d.title),1)]),_:2},1024)):(a(),k(m(ot),{key:1,"as-child":""},{default:v(()=>[_(m(w),{href:d.href??"#"},{default:v(()=>[C(x(d.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),g!==t.breadcrumbs.length-1?(a(),k(m(lt),{key:0})):y("",!0)],64))),128))]),_:1})]),_:1}))}}),ct={class:"flex h-screen bg-gray-100"},dt={key:1,class:"w-64 bg-white border-r border-gray-200 flex items-center justify-center"},mt={class:"flex-1 flex flex-col overflow-hidden"},pt={class:"bg-white shadow-sm border-b border-gray-200"},ht={class:"px-4 py-2"},vt={class:"flex items-center justify-between"},ft={class:"flex items-center space-x-3"},gt={class:"text-sm text-gray-700"},_t={class:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100"},yt={__name:"AppSidebarLayout",props:{breadcrumbs:{type:Array,default:()=>[]}},setup(u){const t=us(),o=l(()=>{var d;return((d=t.props.auth)==null?void 0:d.user)||null});return(d,g)=>{var f;return a(),i("div",ct,[o.value?(a(),k(st,{key:0,user:o.value},null,8,["user"])):(a(),i("div",dt,g[0]||(g[0]=[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))),r("div",mt,[r("header",pt,[r("div",ht,[r("div",vt,[r("div",null,[u.breadcrumbs.length>0?(a(),k(ut,{key:0,breadcrumbs:u.breadcrumbs},null,8,["breadcrumbs"])):y("",!0)]),r("div",ft,[r("span",gt,"Welcome, "+x(((f=o.value)==null?void 0:f.name)||"Guest"),1)])])])]),r("main",_t,[$(d.$slots,"default")])])])}}},Mt=z({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(u){return(t,o)=>(a(),k(yt,{breadcrumbs:t.breadcrumbs},{default:v(()=>[$(t.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{hs as M,Mt as _};
