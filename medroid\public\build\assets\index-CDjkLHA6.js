import{a as k,P as p,c as C}from"./Primitive-lW_nm-2e.js";import{N as z,y as V,e as w,u,A as _,g as N,Q as j}from"./vendor-DwpQ5WHX.js";const h=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,m=k,B=(t,n)=>e=>{var d;if((n==null?void 0:n.variants)==null)return m(t,e==null?void 0:e.class,e==null?void 0:e.className);const{variants:c,defaultVariants:r}=n,b=Object.keys(c).map(a=>{const s=e==null?void 0:e[a],o=r==null?void 0:r[a];if(s===null)return null;const i=h(s)||h(o);return c[a][i]}),v=e&&Object.entries(e).reduce((a,s)=>{let[o,i]=s;return i===void 0||(a[o]=i),a},{}),f=n==null||(d=n.compoundVariants)===null||d===void 0?void 0:d.reduce((a,s)=>{let{class:o,className:i,...y}=s;return Object.entries(y).every(x=>{let[g,l]=x;return Array.isArray(l)?l.includes({...r,...v}[g]):{...r,...v}[g]===l})?[...a,o,i]:a},[]);return m(t,b,f,e==null?void 0:e.class,e==null?void 0:e.className)},S=z({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(t){const n=t;return(e,d)=>(w(),V(u(p),{"data-slot":"button",as:e.as,"as-child":e.asChild,class:_(u(C)(u(O)({variant:e.variant,size:e.size}),n.class))},{default:N(()=>[j(e.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),O=B("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});export{S as _};
