import{_ as E}from"./AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js";import{r as x,o as U,d as c,e as n,i as e,t as p,j as D,n as u,v as g,l as b,s as h,y as C,g as j,F as M,p as A,A as V,x as P}from"./vendor-DwpQ5WHX.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-lW_nm-2e.js";import"./createLucideIcon-Di250PjL.js";const N={class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50"},O={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},T={class:"mt-3"},B={class:"flex justify-between items-center mb-4"},F={class:"text-lg font-medium text-gray-900"},L={class:"grid grid-cols-2 gap-4"},q={class:"border border-gray-200 rounded-md p-4"},z={class:"flex items-center mb-3"},I={key:0,class:"ml-6 space-y-2"},Y={class:"flex items-center"},G={class:"flex items-center"},R={class:"flex items-center"},H={class:"border border-gray-200 rounded-md p-4"},J={class:"grid grid-cols-2 gap-4"},K={class:"flex items-center"},Q={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},W=["disabled"],X={__name:"ProviderServiceModal",props:{service:{type:Object,default:null}},emits:["close","saved"],setup(k,{emit:w}){const m=k,f=w,v=x(!1),s=x({name:"",description:"",category:"",price:0,duration:30,is_telemedicine:!1,supports_video:!1,supports_audio:!1,supports_chat:!1,active:!0,discount_percentage:null,discount_valid_until:""}),y=async()=>{var a,t,o,_,$;v.value=!0;try{const d=m.service?`/save-service/${m.service.id}`:"/save-service",r=m.service?"put":"post";console.log("Saving service with data:",s.value),console.log("Using URL:",d,"Method:",r);const i=await window.axios[r](d,s.value);console.log("Service saved successfully:",i.data),f("saved")}catch(d){if(console.error("Error saving service:",d),console.error("Error response:",d.response),((a=d.response)==null?void 0:a.status)===419)alert("Session expired. Please refresh the page and try again."),window.location.reload();else if((o=(t=d.response)==null?void 0:t.data)!=null&&o.errors){const r=Object.values(d.response.data.errors).flat();alert(`Validation errors:
`+r.join(`
`))}else($=(_=d.response)==null?void 0:_.data)!=null&&$.message?alert("Error: "+d.response.data.message):alert("Error saving service. Please try again.")}finally{v.value=!1}};return U(()=>{if(m.service&&(Object.keys(s.value).forEach(a=>{m.service[a]!==void 0&&(s.value[a]=m.service[a])}),m.service.discount_valid_until)){const a=new Date(m.service.discount_valid_until);s.value.discount_valid_until=a.toISOString().slice(0,16)}}),(a,t)=>(n(),c("div",N,[e("div",O,[e("div",T,[e("div",B,[e("h3",F,p(k.service?"Edit Service":"Create New Service"),1),e("button",{onClick:t[0]||(t[0]=o=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600"},t[14]||(t[14]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("form",{onSubmit:D(y,["prevent"]),class:"space-y-4"},[e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700"},"Service Name *",-1)),u(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.name=o),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., General Consultation, Cardiology Check-up"},null,512),[[g,s.value.name]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700"},"Description",-1)),u(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.description=o),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe what this service includes..."},null,512),[[g,s.value.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700"},"Category",-1)),u(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.value.category=o),type:"text",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., Consultation, Therapy, Diagnostic"},null,512),[[g,s.value.category]])]),e("div",L,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700"},"Price ($) *",-1)),u(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.price=o),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[g,s.value.price,void 0,{number:!0}]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700"},"Duration (minutes) *",-1)),u(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.duration=o),type:"number",min:"5",step:"5",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"30"},null,512),[[g,s.value.duration,void 0,{number:!0}]])])]),e("div",q,[e("div",z,[u(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.is_telemedicine=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[h,s.value.is_telemedicine]]),t[20]||(t[20]=e("label",{class:"ml-2 block text-sm font-medium text-gray-900"}," Offer as Telemedicine Service ",-1))]),s.value.is_telemedicine?(n(),c("div",I,[t[24]||(t[24]=e("div",{class:"text-sm text-gray-600 mb-2"},"Select supported communication methods:",-1)),e("div",Y,[u(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.supports_video=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[h,s.value.supports_video]]),t[21]||(t[21]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Video Calls ",-1))]),e("div",G,[u(e("input",{"onUpdate:modelValue":t[8]||(t[8]=o=>s.value.supports_audio=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[h,s.value.supports_audio]]),t[22]||(t[22]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Audio Calls ",-1))]),e("div",R,[u(e("input",{"onUpdate:modelValue":t[9]||(t[9]=o=>s.value.supports_chat=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[h,s.value.supports_chat]]),t[23]||(t[23]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Text Chat ",-1))])])):b("",!0)]),e("div",H,[t[27]||(t[27]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Promotional Pricing (Optional)",-1)),e("div",J,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700"},"Discount (%)",-1)),u(e("input",{"onUpdate:modelValue":t[10]||(t[10]=o=>s.value.discount_percentage=o),type:"number",step:"0.01",min:"0",max:"100",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[g,s.value.discount_percentage,void 0,{number:!0}]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700"},"Valid Until",-1)),u(e("input",{"onUpdate:modelValue":t[11]||(t[11]=o=>s.value.discount_valid_until=o),type:"datetime-local",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[g,s.value.discount_valid_until]])])])]),e("div",K,[u(e("input",{"onUpdate:modelValue":t[12]||(t[12]=o=>s.value.active=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[h,s.value.active]]),t[28]||(t[28]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Make this service active and available for booking ",-1))]),e("div",Q,[e("button",{type:"button",onClick:t[13]||(t[13]=o=>a.$emit("close")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:v.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},p(v.value?"Saving...":k.service?"Update Service":"Create Service"),9,W)])],32)])])]))}},Z={class:"py-12"},ee={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},te={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg"},se={class:"p-6 lg:p-8 bg-white border-b border-gray-200"},oe={class:"flex justify-between items-center mb-6"},le={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},re={class:"p-6"},ie={class:"flex justify-between items-start mb-4"},ne={class:"text-lg font-semibold text-gray-900"},ae={class:"text-gray-600 text-sm mb-4"},de={class:"space-y-2 mb-4"},ue={class:"flex justify-between text-sm"},ce={class:"font-medium"},me={class:"flex justify-between text-sm"},pe={class:"font-medium text-green-600"},ve={class:"flex justify-between text-sm"},be={class:"font-medium"},ge={key:0,class:"mb-4"},xe={class:"flex flex-wrap gap-1"},fe={key:0,class:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"},ye={key:1,class:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded"},we={key:2,class:"px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded"},_e={key:1,class:"mb-4 p-2 bg-yellow-50 rounded"},he={class:"text-xs text-yellow-800"},ke={key:0},$e={class:"flex justify-between items-center pt-4 border-t border-gray-100"},Se=["onClick"],Ce=["onClick"],Ve=["onClick"],Ue={key:1,class:"text-center py-12"},Pe={__name:"Services",setup(k){const w=x([]),m=x(!1),f=x(!1),v=x(!1),s=x(null),y=async()=>{m.value=!0;try{console.log("Loading services...");const r=await window.axios.get("/services-list");console.log("Services loaded:",r.data),w.value=r.data}catch(r){console.error("Error loading services:",r),console.error("Error response:",r.response)}finally{m.value=!1}},a=r=>{s.value=r,v.value=!0},t=async r=>{try{await window.axios.put(`/save-service/${r.id}`,{...r,active:!r.active}),await y()}catch(i){console.error("Error updating service status:",i),alert("Error updating service status")}},o=async r=>{if(confirm(`Are you sure you want to delete "${r.name}"?`))try{await window.axios.delete(`/delete-service/${r.id}`),await y()}catch(i){console.error("Error deleting service:",i),alert("Error deleting service")}},_=()=>{f.value=!1,v.value=!1,s.value=null},$=()=>{_(),y()},d=r=>new Date(r).toLocaleDateString();return U(()=>{y()}),(r,i)=>(n(),C(E,{title:"My Services"},{default:j(()=>[e("div",Z,[e("div",ee,[e("div",te,[e("div",se,[e("div",oe,[i[2]||(i[2]=e("h1",{class:"text-2xl font-medium text-gray-900"},"My Services",-1)),e("button",{onClick:i[0]||(i[0]=l=>f.value=!0),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add New Service ")]),w.value.length>0?(n(),c("div",le,[(n(!0),c(M,null,A(w.value,l=>(n(),c("div",{key:l.id,class:"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"},[e("div",re,[e("div",ie,[e("h3",ne,p(l.name),1),e("span",{class:V(["px-2 py-1 text-xs font-semibold rounded-full",l.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},p(l.active?"Active":"Inactive"),3)]),e("p",ae,p(l.description),1),e("div",de,[e("div",ue,[i[3]||(i[3]=e("span",{class:"text-gray-500"},"Category:",-1)),e("span",ce,p(l.category||"Uncategorized"),1)]),e("div",me,[i[4]||(i[4]=e("span",{class:"text-gray-500"},"Price:",-1)),e("span",pe,"$"+p(l.price),1)]),e("div",ve,[i[5]||(i[5]=e("span",{class:"text-gray-500"},"Duration:",-1)),e("span",be,p(l.duration)+" minutes",1)])]),l.is_telemedicine?(n(),c("div",ge,[i[6]||(i[6]=e("div",{class:"text-xs text-gray-500 mb-2"},"Telemedicine Features:",-1)),e("div",xe,[l.supports_video?(n(),c("span",fe," Video ")):b("",!0),l.supports_audio?(n(),c("span",ye," Audio ")):b("",!0),l.supports_chat?(n(),c("span",we," Chat ")):b("",!0)])])):b("",!0),l.discount_percentage?(n(),c("div",_e,[e("div",he,[P(p(l.discount_percentage)+"% discount ",1),l.discount_valid_until?(n(),c("span",ke," until "+p(d(l.discount_valid_until)),1)):b("",!0)])])):b("",!0),e("div",$e,[e("button",{onClick:S=>a(l),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," Edit ",8,Se),e("button",{onClick:S=>t(l),class:V(["text-sm font-medium",l.active?"text-red-600 hover:text-red-800":"text-green-600 hover:text-green-800"])},p(l.active?"Deactivate":"Activate"),11,Ce),e("button",{onClick:S=>o(l),class:"text-red-600 hover:text-red-800 text-sm font-medium"}," Delete ",8,Ve)])])]))),128))])):(n(),c("div",Ue,[i[7]||(i[7]=e("div",{class:"text-gray-500 mb-4"},"You haven't created any services yet.",-1)),e("button",{onClick:i[1]||(i[1]=l=>f.value=!0),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Create Your First Service ")]))])])])]),f.value||v.value?(n(),C(X,{key:0,service:s.value,onClose:_,onSaved:$},null,8,["service"])):b("",!0)]),_:1}))}};export{Pe as default};
