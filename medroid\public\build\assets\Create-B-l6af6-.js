import{r as y,o as S,d as l,e as a,f as x,u as f,m as B,g as c,i as t,j as D,l as u,n as i,v as n,t as d,F as v,p as V,q as C,s as U,P as w,x as h,y as M}from"./vendor-DwpQ5WHX.js";import{_ as N}from"./AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-lW_nm-2e.js";import"./createLucideIcon-Di250PjL.js";const E={class:"flex items-center justify-between"},F={class:"flex mt-2","aria-label":"Breadcrumb"},j={class:"inline-flex items-center space-x-1 md:space-x-3"},L={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},T={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},W={class:"py-12"},z={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},A={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},H={class:"p-6 text-gray-900 dark:text-gray-100"},K={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={key:0,class:"mt-1 text-sm text-red-600"},$={key:0,class:"mt-1 text-sm text-red-600"},G=["value"],I={key:0,class:"mt-1 text-sm text-red-600"},J={key:0,class:"mt-1 text-sm text-red-600"},O={key:0,class:"mt-1 text-sm text-red-600"},R={key:0,class:"mt-1 text-sm text-red-600"},X={key:0,class:"mt-1 text-sm text-red-600"},Y={key:0,class:"mt-1 text-sm text-red-600"},Z={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ee={key:0,class:"mt-1 text-sm text-red-600"},te={key:0,class:"mt-1 text-sm text-red-600"},re={key:0,class:"mt-1 text-sm text-red-600"},se={class:"flex items-center space-x-6"},oe={class:"flex items-center"},ae={class:"flex items-center"},le={class:"flex justify-end space-x-3"},de=["disabled"],ce={__name:"Create",setup(ue){const k=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Create Product",href:"/admin/products/create"}],b=y(!1),_=y([]),s=y({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:""}),o=y({}),P=async()=>{try{const m=await window.axios.get("/admin/products/create");_.value=m.data.categories||[]}catch(m){console.error("Error fetching categories:",m)}},q=async()=>{var m,e,r,g;b.value=!0,o.value={};try{const p=await window.axios.post("/admin/save-product",s.value);p.data.success?window.location.href="/admin/products":alert("Error creating product: "+p.data.message)}catch(p){(e=(m=p.response)==null?void 0:m.data)!=null&&e.errors?o.value=p.response.data.errors:alert("Error creating product: "+(((g=(r=p.response)==null?void 0:r.data)==null?void 0:g.message)||p.message))}finally{b.value=!1}};return S(()=>{P()}),(m,e)=>(a(),l(v,null,[x(f(B),{title:"Create Product"}),x(N,null,{header:c(()=>[t("div",E,[t("div",null,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Create Product ",-1)),t("nav",F,[t("ol",j,[(a(),l(v,null,V(k,(r,g)=>t("li",{key:g,class:"inline-flex items-center"},[g<k.length-1?(a(),M(f(w),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[h(d(r.title),1)]),_:2},1032,["href"])):(a(),l("span",L,d(r.title),1)),g<k.length-1?(a(),l("svg",T,e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):u("",!0)])),64))])])]),x(f(w),{href:"/admin/products",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:c(()=>e[15]||(e[15]=[h(" Back to Products ")])),_:1})])]),default:c(()=>[t("div",W,[t("div",z,[t("div",A,[t("div",H,[t("form",{onSubmit:D(q,["prevent"]),class:"space-y-6"},[t("div",K,[t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),i(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>s.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.name]]),o.value.name?(a(),l("p",Q,d(o.value.name[0]),1)):u("",!0)]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),i(t("input",{"onUpdate:modelValue":e[1]||(e[1]=r=>s.value.sku=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.sku]]),o.value.sku?(a(),l("p",$,d(o.value.sku[0]),1)):u("",!0)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),i(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>s.value.category_id=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[18]||(e[18]=t("option",{value:""},"Select Category",-1)),(a(!0),l(v,null,V(_.value,r=>(a(),l("option",{key:r.id,value:r.id},d(r.name),9,G))),128))],512),[[C,s.value.category_id]]),o.value.category_id?(a(),l("p",I,d(o.value.category_id[0]),1)):u("",!0)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Type *",-1)),i(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>s.value.type=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[20]||(e[20]=[t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[C,s.value.type]]),o.value.type?(a(),l("p",J,d(o.value.type[0]),1)):u("",!0)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),i(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>s.value.price=r),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.price]]),o.value.price?(a(),l("p",O,d(o.value.price[0]),1)):u("",!0)]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),i(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>s.value.sale_price=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.sale_price]]),o.value.sale_price?(a(),l("p",R,d(o.value.sale_price[0]),1)):u("",!0)])]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),i(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>s.value.description=r),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.description]]),o.value.description?(a(),l("p",X,d(o.value.description[0]),1)):u("",!0)]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),i(t("textarea",{"onUpdate:modelValue":e[7]||(e[7]=r=>s.value.short_description=r),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.short_description]]),o.value.short_description?(a(),l("p",Y,d(o.value.short_description[0]),1)):u("",!0)]),s.value.type==="physical"?(a(),l("div",Z,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity",-1)),i(t("input",{"onUpdate:modelValue":e[8]||(e[8]=r=>s.value.stock_quantity=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.stock_quantity]]),o.value.stock_quantity?(a(),l("p",ee,d(o.value.stock_quantity[0]),1)):u("",!0)]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),i(t("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>s.value.weight=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.weight]]),o.value.weight?(a(),l("p",te,d(o.value.weight[0]),1)):u("",!0)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),i(t("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>s.value.dimensions=r),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[n,s.value.dimensions]]),o.value.dimensions?(a(),l("p",re,d(o.value.dimensions[0]),1)):u("",!0)])])):u("",!0),t("div",se,[t("label",oe,[i(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>s.value.is_featured=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[U,s.value.is_featured]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",ae,[i(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>s.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[U,s.value.is_active]]),e[30]||(e[30]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",le,[x(f(w),{href:"/admin/products",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:c(()=>e[31]||(e[31]=[h(" Cancel ")])),_:1}),t("button",{type:"submit",disabled:b.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},d(b.value?"Creating...":"Create Product"),9,de)])],32)])])])])]),_:1})],64))}};export{ce as default};
