import{N as g,r as f,V as v,y,e as V,g as a,f as r,u as s,m as b,i as t,j as k,x as d,ak as x,n as C,U as I}from"./vendor-DwpQ5WHX.js";import{_ as u}from"./InputError.vue_vue_type_script_setup_true_lang-DSYw3Bxn.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js";import{_ as $}from"./Layout.vue_vue_type_script_setup_true_lang-BFFH9o3p.js";import{_ as N}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-D0cv1GJg.js";import{_ as P}from"./index-CDjkLHA6.js";import{_ as m,a as c}from"./Label.vue_vue_type_script_setup_true_lang-L1W7djSO.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-lW_nm-2e.js";import"./createLucideIcon-Di250PjL.js";import"./index-0ZTvHY_i.js";const U={class:"space-y-6"},E={class:"grid gap-2"},T={class:"grid gap-2"},B={class:"grid gap-2"},M={class:"flex items-center gap-4"},H={class:"text-sm text-neutral-600"},Q=g({__name:"Password",setup(L){const w=[{title:"Password settings",href:"/settings/password"}],l=f(null),i=f(null),e=v({current_password:"",password:"",password_confirmation:""}),_=()=>{e.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>e.reset(),onError:p=>{p.password&&(e.reset("password","password_confirmation"),l.value instanceof HTMLInputElement&&l.value.focus()),p.current_password&&(e.reset("current_password"),i.value instanceof HTMLInputElement&&i.value.focus())}})};return(p,o)=>(V(),y(S,{breadcrumbs:w},{default:a(()=>[r(s(b),{title:"Password settings"}),r($,null,{default:a(()=>[t("div",U,[r(N,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),t("form",{onSubmit:k(_,["prevent"]),class:"space-y-6"},[t("div",E,[r(s(m),{for:"current_password"},{default:a(()=>o[3]||(o[3]=[d("Current password")])),_:1}),r(s(c),{id:"current_password",ref_key:"currentPasswordInput",ref:i,modelValue:s(e).current_password,"onUpdate:modelValue":o[0]||(o[0]=n=>s(e).current_password=n),type:"password",class:"mt-1 block w-full",autocomplete:"current-password",placeholder:"Current password"},null,8,["modelValue"]),r(u,{message:s(e).errors.current_password},null,8,["message"])]),t("div",T,[r(s(m),{for:"password"},{default:a(()=>o[4]||(o[4]=[d("New password")])),_:1}),r(s(c),{id:"password",ref_key:"passwordInput",ref:l,modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=n=>s(e).password=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"New password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password},null,8,["message"])]),t("div",B,[r(s(m),{for:"password_confirmation"},{default:a(()=>o[5]||(o[5]=[d("Confirm password")])),_:1}),r(s(c),{id:"password_confirmation",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=n=>s(e).password_confirmation=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"Confirm password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password_confirmation},null,8,["message"])]),t("div",M,[r(s(P),{disabled:s(e).processing},{default:a(()=>o[6]||(o[6]=[d("Save password")])),_:1},8,["disabled"]),r(x,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:a(()=>[C(t("p",H,"Saved.",512),[[I,s(e).recentlySuccessful]])]),_:1})])],32)])]),_:1})]),_:1}))}});export{Q as default};
