import{E as Lt,r as V,a as Z,w as Ae,C as X,d as N,l as ee,e as L,i as r,j as Me,t as O,F as ae,p as fe,A as ve,n as Ze,v as qt,x as It,I as Ft,o as Ut,H as jt,f as ue,u as zt,m as Vt,g as Ht,q as nt}from"./vendor-DwpQ5WHX.js";import{M as ke,_ as Ot}from"./AppLayout.vue_vue_type_script_setup_true_lang-CSJB8fXy.js";import{C as Kt}from"./ChatInput-B1to_S1K.js";import{_ as Yt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-lW_nm-2e.js";import"./createLucideIcon-Di250PjL.js";var pe={},De,ot;function Jt(){return ot||(ot=1,De=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),De}var Ie={},de={},rt;function ge(){if(rt)return de;rt=1;let s;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return de.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17},de.getSymbolTotalCodewords=function(t){return o[t]},de.getBCHDigit=function(i){let t=0;for(;i!==0;)t++,i>>>=1;return t},de.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');s=t},de.isKanjiModeEnabled=function(){return typeof s<"u"},de.toSJIS=function(t){return s(t)},de}var Re={},st;function et(){return st||(st=1,function(s){s.L={bit:1},s.M={bit:0},s.Q={bit:3},s.H={bit:2};function o(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return s.L;case"m":case"medium":return s.M;case"q":case"quartile":return s.Q;case"h":case"high":return s.H;default:throw new Error("Unknown EC Level: "+i)}}s.isValid=function(t){return t&&typeof t.bit<"u"&&t.bit>=0&&t.bit<4},s.from=function(t,e){if(s.isValid(t))return t;try{return o(t)}catch{return e}}}(Re)),Re}var Be,at;function Qt(){if(at)return Be;at=1;function s(){this.buffer=[],this.length=0}return s.prototype={get:function(o){const i=Math.floor(o/8);return(this.buffer[i]>>>7-o%8&1)===1},put:function(o,i){for(let t=0;t<i;t++)this.putBit((o>>>i-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const i=Math.floor(this.length/8);this.buffer.length<=i&&this.buffer.push(0),o&&(this.buffer[i]|=128>>>this.length%8),this.length++}},Be=s,Be}var $e,it;function Gt(){if(it)return $e;it=1;function s(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return s.prototype.set=function(o,i,t,e){const n=o*this.size+i;this.data[n]=t,e&&(this.reservedBit[n]=!0)},s.prototype.get=function(o,i){return this.data[o*this.size+i]},s.prototype.xor=function(o,i,t){this.data[o*this.size+i]^=t},s.prototype.isReserved=function(o,i){return this.reservedBit[o*this.size+i]},$e=s,$e}var Pe={},lt;function Wt(){return lt||(lt=1,function(s){const o=ge().getSymbolSize;s.getRowColCoords=function(t){if(t===1)return[];const e=Math.floor(t/7)+2,n=o(t),a=n===145?26:Math.ceil((n-13)/(2*e-2))*2,l=[n-7];for(let c=1;c<e-1;c++)l[c]=l[c-1]-a;return l.push(6),l.reverse()},s.getPositions=function(t){const e=[],n=s.getRowColCoords(t),a=n.length;for(let l=0;l<a;l++)for(let c=0;c<a;c++)l===0&&c===0||l===0&&c===a-1||l===a-1&&c===0||e.push([n[l],n[c]]);return e}}(Pe)),Pe}var Ne={},ct;function Xt(){if(ct)return Ne;ct=1;const s=ge().getSymbolSize,o=7;return Ne.getPositions=function(t){const e=s(t);return[[0,0],[e-o,0],[0,e-o]]},Ne}var Le={},ut;function Zt(){return ut||(ut=1,function(s){s.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};s.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},s.from=function(e){return s.isValid(e)?parseInt(e,10):void 0},s.getPenaltyN1=function(e){const n=e.size;let a=0,l=0,c=0,p=null,w=null;for(let S=0;S<n;S++){l=c=0,p=w=null;for(let _=0;_<n;_++){let h=e.get(S,_);h===p?l++:(l>=5&&(a+=o.N1+(l-5)),p=h,l=1),h=e.get(_,S),h===w?c++:(c>=5&&(a+=o.N1+(c-5)),w=h,c=1)}l>=5&&(a+=o.N1+(l-5)),c>=5&&(a+=o.N1+(c-5))}return a},s.getPenaltyN2=function(e){const n=e.size;let a=0;for(let l=0;l<n-1;l++)for(let c=0;c<n-1;c++){const p=e.get(l,c)+e.get(l,c+1)+e.get(l+1,c)+e.get(l+1,c+1);(p===4||p===0)&&a++}return a*o.N2},s.getPenaltyN3=function(e){const n=e.size;let a=0,l=0,c=0;for(let p=0;p<n;p++){l=c=0;for(let w=0;w<n;w++)l=l<<1&2047|e.get(p,w),w>=10&&(l===1488||l===93)&&a++,c=c<<1&2047|e.get(w,p),w>=10&&(c===1488||c===93)&&a++}return a*o.N3},s.getPenaltyN4=function(e){let n=0;const a=e.data.length;for(let c=0;c<a;c++)n+=e.data[c];return Math.abs(Math.ceil(n*100/a/5)-10)*o.N4};function i(t,e,n){switch(t){case s.Patterns.PATTERN000:return(e+n)%2===0;case s.Patterns.PATTERN001:return e%2===0;case s.Patterns.PATTERN010:return n%3===0;case s.Patterns.PATTERN011:return(e+n)%3===0;case s.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2===0;case s.Patterns.PATTERN101:return e*n%2+e*n%3===0;case s.Patterns.PATTERN110:return(e*n%2+e*n%3)%2===0;case s.Patterns.PATTERN111:return(e*n%3+(e+n)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}s.applyMask=function(e,n){const a=n.size;for(let l=0;l<a;l++)for(let c=0;c<a;c++)n.isReserved(c,l)||n.xor(c,l,i(e,c,l))},s.getBestMask=function(e,n){const a=Object.keys(s.Patterns).length;let l=0,c=1/0;for(let p=0;p<a;p++){n(p),s.applyMask(p,e);const w=s.getPenaltyN1(e)+s.getPenaltyN2(e)+s.getPenaltyN3(e)+s.getPenaltyN4(e);s.applyMask(p,e),w<c&&(c=w,l=p)}return l}}(Le)),Le}var Ee={},dt;function Rt(){if(dt)return Ee;dt=1;const s=et(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return Ee.getBlocksCount=function(e,n){switch(n){case s.L:return o[(e-1)*4+0];case s.M:return o[(e-1)*4+1];case s.Q:return o[(e-1)*4+2];case s.H:return o[(e-1)*4+3];default:return}},Ee.getTotalCodewordsCount=function(e,n){switch(n){case s.L:return i[(e-1)*4+0];case s.M:return i[(e-1)*4+1];case s.Q:return i[(e-1)*4+2];case s.H:return i[(e-1)*4+3];default:return}},Ee}var qe={},_e={},ft;function en(){if(ft)return _e;ft=1;const s=new Uint8Array(512),o=new Uint8Array(256);return function(){let t=1;for(let e=0;e<255;e++)s[e]=t,o[t]=e,t<<=1,t&256&&(t^=285);for(let e=255;e<512;e++)s[e]=s[e-255]}(),_e.log=function(t){if(t<1)throw new Error("log("+t+")");return o[t]},_e.exp=function(t){return s[t]},_e.mul=function(t,e){return t===0||e===0?0:s[o[t]+o[e]]},_e}var gt;function tn(){return gt||(gt=1,function(s){const o=en();s.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let a=0;a<t.length;a++)for(let l=0;l<e.length;l++)n[a+l]^=o.mul(t[a],e[l]);return n},s.mod=function(t,e){let n=new Uint8Array(t);for(;n.length-e.length>=0;){const a=n[0];for(let c=0;c<e.length;c++)n[c]^=o.mul(e[c],a);let l=0;for(;l<n.length&&n[l]===0;)l++;n=n.slice(l)}return n},s.generateECPolynomial=function(t){let e=new Uint8Array([1]);for(let n=0;n<t;n++)e=s.mul(e,new Uint8Array([1,o.exp(n)]));return e}}(qe)),qe}var Fe,mt;function nn(){if(mt)return Fe;mt=1;const s=tn();function o(i){this.genPoly=void 0,this.degree=i,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(t){this.degree=t,this.genPoly=s.generateECPolynomial(this.degree)},o.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=s.mod(e,this.genPoly),a=this.degree-n.length;if(a>0){const l=new Uint8Array(this.degree);return l.set(n,a),l}return n},Fe=o,Fe}var Ue={},je={},ze={},ht;function Bt(){return ht||(ht=1,ze.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),ze}var oe={},pt;function $t(){if(pt)return oe;pt=1;const s="[0-9]+",o="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const t="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+`)(?:.|[\r
]))+`;oe.KANJI=new RegExp(i,"g"),oe.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),oe.BYTE=new RegExp(t,"g"),oe.NUMERIC=new RegExp(s,"g"),oe.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+i+"$"),n=new RegExp("^"+s+"$"),a=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return oe.testKanji=function(c){return e.test(c)},oe.testNumeric=function(c){return n.test(c)},oe.testAlphanumeric=function(c){return a.test(c)},oe}var vt;function me(){return vt||(vt=1,function(s){const o=Bt(),i=$t();s.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},s.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},s.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},s.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},s.MIXED={bit:-1},s.getCharCountIndicator=function(n,a){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!o.isValid(a))throw new Error("Invalid version: "+a);return a>=1&&a<10?n.ccBits[0]:a<27?n.ccBits[1]:n.ccBits[2]},s.getBestModeForData=function(n){return i.testNumeric(n)?s.NUMERIC:i.testAlphanumeric(n)?s.ALPHANUMERIC:i.testKanji(n)?s.KANJI:s.BYTE},s.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},s.isValid=function(n){return n&&n.bit&&n.ccBits};function t(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return s.NUMERIC;case"alphanumeric":return s.ALPHANUMERIC;case"kanji":return s.KANJI;case"byte":return s.BYTE;default:throw new Error("Unknown mode: "+e)}}s.from=function(n,a){if(s.isValid(n))return n;try{return t(n)}catch{return a}}}(je)),je}var yt;function on(){return yt||(yt=1,function(s){const o=ge(),i=Rt(),t=et(),e=me(),n=Bt(),a=7973,l=o.getBCHDigit(a);function c(_,h,b){for(let v=1;v<=40;v++)if(h<=s.getCapacity(v,b,_))return v}function p(_,h){return e.getCharCountIndicator(_,h)+4}function w(_,h){let b=0;return _.forEach(function(v){const j=p(v.mode,h);b+=j+v.getBitsLength()}),b}function S(_,h){for(let b=1;b<=40;b++)if(w(_,b)<=s.getCapacity(b,h,e.MIXED))return b}s.from=function(h,b){return n.isValid(h)?parseInt(h,10):b},s.getCapacity=function(h,b,v){if(!n.isValid(h))throw new Error("Invalid QR Code version");typeof v>"u"&&(v=e.BYTE);const j=o.getSymbolTotalCodewords(h),B=i.getTotalCodewordsCount(h,b),q=(j-B)*8;if(v===e.MIXED)return q;const I=q-p(v,h);switch(v){case e.NUMERIC:return Math.floor(I/10*3);case e.ALPHANUMERIC:return Math.floor(I/11*2);case e.KANJI:return Math.floor(I/13);case e.BYTE:default:return Math.floor(I/8)}},s.getBestVersionForData=function(h,b){let v;const j=t.from(b,t.M);if(Array.isArray(h)){if(h.length>1)return S(h,j);if(h.length===0)return 1;v=h[0]}else v=h;return c(v.mode,v.getLength(),j)},s.getEncodedBits=function(h){if(!n.isValid(h)||h<7)throw new Error("Invalid QR Code version");let b=h<<12;for(;o.getBCHDigit(b)-l>=0;)b^=a<<o.getBCHDigit(b)-l;return h<<12|b}}(Ue)),Ue}var Ve={},wt;function rn(){if(wt)return Ve;wt=1;const s=ge(),o=1335,i=21522,t=s.getBCHDigit(o);return Ve.getEncodedBits=function(n,a){const l=n.bit<<3|a;let c=l<<10;for(;s.getBCHDigit(c)-t>=0;)c^=o<<s.getBCHDigit(c)-t;return(l<<10|c)^i},Ve}var He={},Oe,bt;function sn(){if(bt)return Oe;bt=1;const s=me();function o(i){this.mode=s.NUMERIC,this.data=i.toString()}return o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e,n,a;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),a=parseInt(n,10),t.put(a,10);const l=this.data.length-e;l>0&&(n=this.data.substr(e),a=parseInt(n,10),t.put(a,l*3+1))},Oe=o,Oe}var Ke,xt;function an(){if(xt)return Ke;xt=1;const s=me(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=s.ALPHANUMERIC,this.data=t}return i.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){let n;for(n=0;n+2<=this.data.length;n+=2){let a=o.indexOf(this.data[n])*45;a+=o.indexOf(this.data[n+1]),e.put(a,11)}this.data.length%2&&e.put(o.indexOf(this.data[n]),6)},Ke=i,Ke}var Ye,_t;function ln(){if(_t)return Ye;_t=1;const s=me();function o(i){this.mode=s.BYTE,typeof i=="string"?this.data=new TextEncoder().encode(i):this.data=new Uint8Array(i)}return o.getBitsLength=function(t){return t*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(i){for(let t=0,e=this.data.length;t<e;t++)i.put(this.data[t],8)},Ye=o,Ye}var Je,Ct;function cn(){if(Ct)return Je;Ct=1;const s=me(),o=ge();function i(t){this.mode=s.KANJI,this.data=t}return i.getBitsLength=function(e){return e*13},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=o.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),t.put(n,13)}},Je=i,Je}var Qe={exports:{}},kt;function un(){return kt||(kt=1,function(s){var o={single_source_shortest_paths:function(i,t,e){var n={},a={};a[t]=0;var l=o.PriorityQueue.make();l.push(t,0);for(var c,p,w,S,_,h,b,v,j;!l.empty();){c=l.pop(),p=c.value,S=c.cost,_=i[p]||{};for(w in _)_.hasOwnProperty(w)&&(h=_[w],b=S+h,v=a[w],j=typeof a[w]>"u",(j||v>b)&&(a[w]=b,l.push(w,b),n[w]=p))}if(typeof e<"u"&&typeof a[e]>"u"){var B=["Could not find a path from ",t," to ",e,"."].join("");throw new Error(B)}return n},extract_shortest_path_from_predecessor_list:function(i,t){for(var e=[],n=t;n;)e.push(n),i[n],n=i[n];return e.reverse(),e},find_path:function(i,t,e){var n=o.single_source_shortest_paths(i,t,e);return o.extract_shortest_path_from_predecessor_list(n,e)},PriorityQueue:{make:function(i){var t=o.PriorityQueue,e={},n;i=i||{};for(n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e.queue=[],e.sorter=i.sorter||t.default_sorter,e},default_sorter:function(i,t){return i.cost-t.cost},push:function(i,t){var e={value:i,cost:t};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};s.exports=o}(Qe)),Qe.exports}var Et;function dn(){return Et||(Et=1,function(s){const o=me(),i=sn(),t=an(),e=ln(),n=cn(),a=$t(),l=ge(),c=un();function p(B){return unescape(encodeURIComponent(B)).length}function w(B,q,I){const R=[];let J;for(;(J=B.exec(I))!==null;)R.push({data:J[0],index:J.index,mode:q,length:J[0].length});return R}function S(B){const q=w(a.NUMERIC,o.NUMERIC,B),I=w(a.ALPHANUMERIC,o.ALPHANUMERIC,B);let R,J;return l.isKanjiModeEnabled()?(R=w(a.BYTE,o.BYTE,B),J=w(a.KANJI,o.KANJI,B)):(R=w(a.BYTE_KANJI,o.BYTE,B),J=[]),q.concat(I,R,J).sort(function(A,D){return A.index-D.index}).map(function(A){return{data:A.data,mode:A.mode,length:A.length}})}function _(B,q){switch(q){case o.NUMERIC:return i.getBitsLength(B);case o.ALPHANUMERIC:return t.getBitsLength(B);case o.KANJI:return n.getBitsLength(B);case o.BYTE:return e.getBitsLength(B)}}function h(B){return B.reduce(function(q,I){const R=q.length-1>=0?q[q.length-1]:null;return R&&R.mode===I.mode?(q[q.length-1].data+=I.data,q):(q.push(I),q)},[])}function b(B){const q=[];for(let I=0;I<B.length;I++){const R=B[I];switch(R.mode){case o.NUMERIC:q.push([R,{data:R.data,mode:o.ALPHANUMERIC,length:R.length},{data:R.data,mode:o.BYTE,length:R.length}]);break;case o.ALPHANUMERIC:q.push([R,{data:R.data,mode:o.BYTE,length:R.length}]);break;case o.KANJI:q.push([R,{data:R.data,mode:o.BYTE,length:p(R.data)}]);break;case o.BYTE:q.push([{data:R.data,mode:o.BYTE,length:p(R.data)}])}}return q}function v(B,q){const I={},R={start:{}};let J=["start"];for(let C=0;C<B.length;C++){const A=B[C],D=[];for(let E=0;E<A.length;E++){const m=A[E],u=""+C+E;D.push(u),I[u]={node:m,lastCount:0},R[u]={};for(let f=0;f<J.length;f++){const T=J[f];I[T]&&I[T].node.mode===m.mode?(R[T][u]=_(I[T].lastCount+m.length,m.mode)-_(I[T].lastCount,m.mode),I[T].lastCount+=m.length):(I[T]&&(I[T].lastCount=m.length),R[T][u]=_(m.length,m.mode)+4+o.getCharCountIndicator(m.mode,q))}}J=D}for(let C=0;C<J.length;C++)R[J[C]].end=0;return{map:R,table:I}}function j(B,q){let I;const R=o.getBestModeForData(B);if(I=o.from(q,R),I!==o.BYTE&&I.bit<R.bit)throw new Error('"'+B+'" cannot be encoded with mode '+o.toString(I)+`.
 Suggested mode is: `+o.toString(R));switch(I===o.KANJI&&!l.isKanjiModeEnabled()&&(I=o.BYTE),I){case o.NUMERIC:return new i(B);case o.ALPHANUMERIC:return new t(B);case o.KANJI:return new n(B);case o.BYTE:return new e(B)}}s.fromArray=function(q){return q.reduce(function(I,R){return typeof R=="string"?I.push(j(R,null)):R.data&&I.push(j(R.data,R.mode)),I},[])},s.fromString=function(q,I){const R=S(q,l.isKanjiModeEnabled()),J=b(R),C=v(J,I),A=c.find_path(C.map,"start","end"),D=[];for(let E=1;E<A.length-1;E++)D.push(C.table[A[E]].node);return s.fromArray(h(D))},s.rawSplit=function(q){return s.fromArray(S(q,l.isKanjiModeEnabled()))}}(He)),He}var Tt;function fn(){if(Tt)return Ie;Tt=1;const s=ge(),o=et(),i=Qt(),t=Gt(),e=Wt(),n=Xt(),a=Zt(),l=Rt(),c=nn(),p=on(),w=rn(),S=me(),_=dn();function h(C,A){const D=C.size,E=n.getPositions(A);for(let m=0;m<E.length;m++){const u=E[m][0],f=E[m][1];for(let T=-1;T<=7;T++)if(!(u+T<=-1||D<=u+T))for(let P=-1;P<=7;P++)f+P<=-1||D<=f+P||(T>=0&&T<=6&&(P===0||P===6)||P>=0&&P<=6&&(T===0||T===6)||T>=2&&T<=4&&P>=2&&P<=4?C.set(u+T,f+P,!0,!0):C.set(u+T,f+P,!1,!0))}}function b(C){const A=C.size;for(let D=8;D<A-8;D++){const E=D%2===0;C.set(D,6,E,!0),C.set(6,D,E,!0)}}function v(C,A){const D=e.getPositions(A);for(let E=0;E<D.length;E++){const m=D[E][0],u=D[E][1];for(let f=-2;f<=2;f++)for(let T=-2;T<=2;T++)f===-2||f===2||T===-2||T===2||f===0&&T===0?C.set(m+f,u+T,!0,!0):C.set(m+f,u+T,!1,!0)}}function j(C,A){const D=C.size,E=p.getEncodedBits(A);let m,u,f;for(let T=0;T<18;T++)m=Math.floor(T/3),u=T%3+D-8-3,f=(E>>T&1)===1,C.set(m,u,f,!0),C.set(u,m,f,!0)}function B(C,A,D){const E=C.size,m=w.getEncodedBits(A,D);let u,f;for(u=0;u<15;u++)f=(m>>u&1)===1,u<6?C.set(u,8,f,!0):u<8?C.set(u+1,8,f,!0):C.set(E-15+u,8,f,!0),u<8?C.set(8,E-u-1,f,!0):u<9?C.set(8,15-u-1+1,f,!0):C.set(8,15-u-1,f,!0);C.set(E-8,8,1,!0)}function q(C,A){const D=C.size;let E=-1,m=D-1,u=7,f=0;for(let T=D-1;T>0;T-=2)for(T===6&&T--;;){for(let P=0;P<2;P++)if(!C.isReserved(m,T-P)){let Y=!1;f<A.length&&(Y=(A[f]>>>u&1)===1),C.set(m,T-P,Y),u--,u===-1&&(f++,u=7)}if(m+=E,m<0||D<=m){m-=E,E=-E;break}}}function I(C,A,D){const E=new i;D.forEach(function(P){E.put(P.mode.bit,4),E.put(P.getLength(),S.getCharCountIndicator(P.mode,C)),P.write(E)});const m=s.getSymbolTotalCodewords(C),u=l.getTotalCodewordsCount(C,A),f=(m-u)*8;for(E.getLengthInBits()+4<=f&&E.put(0,4);E.getLengthInBits()%8!==0;)E.putBit(0);const T=(f-E.getLengthInBits())/8;for(let P=0;P<T;P++)E.put(P%2?17:236,8);return R(E,C,A)}function R(C,A,D){const E=s.getSymbolTotalCodewords(A),m=l.getTotalCodewordsCount(A,D),u=E-m,f=l.getBlocksCount(A,D),T=E%f,P=f-T,Y=Math.floor(E/f),re=Math.floor(u/f),Te=re+1,Ce=Y-re,Se=new c(Ce);let ie=0;const he=new Array(f),ye=new Array(f);let we=0;const x=new Uint8Array(C.buffer);for(let $=0;$<f;$++){const k=$<P?re:Te;he[$]=x.slice(ie,ie+k),ye[$]=Se.encode(he[$]),ie+=k,we=Math.max(we,k)}const d=new Uint8Array(E);let g=0,y,M;for(y=0;y<we;y++)for(M=0;M<f;M++)y<he[M].length&&(d[g++]=he[M][y]);for(y=0;y<Ce;y++)for(M=0;M<f;M++)d[g++]=ye[M][y];return d}function J(C,A,D,E){let m;if(Array.isArray(C))m=_.fromArray(C);else if(typeof C=="string"){let Y=A;if(!Y){const re=_.rawSplit(C);Y=p.getBestVersionForData(re,D)}m=_.fromString(C,Y||40)}else throw new Error("Invalid data");const u=p.getBestVersionForData(m,D);if(!u)throw new Error("The amount of data is too big to be stored in a QR Code");if(!A)A=u;else if(A<u)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+u+`.
`);const f=I(A,D,m),T=s.getSymbolSize(A),P=new t(T);return h(P,A),b(P),v(P,A),B(P,D,0),A>=7&&j(P,A),q(P,f),isNaN(E)&&(E=a.getBestMask(P,B.bind(null,P,D))),a.applyMask(E,P),B(P,D,E),{modules:P,version:A,errorCorrectionLevel:D,maskPattern:E,segments:m}}return Ie.create=function(A,D){if(typeof A>"u"||A==="")throw new Error("No input text");let E=o.M,m,u;return typeof D<"u"&&(E=o.from(D.errorCorrectionLevel,o.M),m=p.from(D.version),u=a.from(D.maskPattern),D.toSJISFunc&&s.setToSJISFunction(D.toSJISFunc)),J(A,m,E,u)},Ie}var Ge={},We={},St;function Pt(){return St||(St=1,function(s){function o(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let t=i.slice().replace("#","").split("");if(t.length<3||t.length===5||t.length>8)throw new Error("Invalid hex color: "+i);(t.length===3||t.length===4)&&(t=Array.prototype.concat.apply([],t.map(function(n){return[n,n]}))),t.length===6&&t.push("F","F");const e=parseInt(t.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+t.slice(0,6).join("")}}s.getOptions=function(t){t||(t={}),t.color||(t.color={});const e=typeof t.margin>"u"||t.margin===null||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,a=t.scale||4;return{width:n,scale:n?4:a,margin:e,color:{dark:o(t.color.dark||"#000000ff"),light:o(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},s.getScale=function(t,e){return e.width&&e.width>=t+e.margin*2?e.width/(t+e.margin*2):e.scale},s.getImageWidth=function(t,e){const n=s.getScale(t,e);return Math.floor((t+e.margin*2)*n)},s.qrToImageData=function(t,e,n){const a=e.modules.size,l=e.modules.data,c=s.getScale(a,n),p=Math.floor((a+n.margin*2)*c),w=n.margin*c,S=[n.color.light,n.color.dark];for(let _=0;_<p;_++)for(let h=0;h<p;h++){let b=(_*p+h)*4,v=n.color.light;if(_>=w&&h>=w&&_<p-w&&h<p-w){const j=Math.floor((_-w)/c),B=Math.floor((h-w)/c);v=S[l[j*a+B]?1:0]}t[b++]=v.r,t[b++]=v.g,t[b++]=v.b,t[b]=v.a}}}(We)),We}var At;function gn(){return At||(At=1,function(s){const o=Pt();function i(e,n,a){e.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=a,n.width=a,n.style.height=a+"px",n.style.width=a+"px"}function t(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}s.render=function(n,a,l){let c=l,p=a;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),a||(p=t()),c=o.getOptions(c);const w=o.getImageWidth(n.modules.size,c),S=p.getContext("2d"),_=S.createImageData(w,w);return o.qrToImageData(_.data,n,c),i(S,p,w),S.putImageData(_,0,0),p},s.renderToDataURL=function(n,a,l){let c=l;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),c||(c={});const p=s.render(n,a,c),w=c.type||"image/png",S=c.rendererOpts||{};return p.toDataURL(w,S.quality)}}(Ge)),Ge}var Xe={},Mt;function mn(){if(Mt)return Xe;Mt=1;const s=Pt();function o(e,n){const a=e.a/255,l=n+'="'+e.hex+'"';return a<1?l+" "+n+'-opacity="'+a.toFixed(2).slice(1)+'"':l}function i(e,n,a){let l=e+n;return typeof a<"u"&&(l+=" "+a),l}function t(e,n,a){let l="",c=0,p=!1,w=0;for(let S=0;S<e.length;S++){const _=Math.floor(S%n),h=Math.floor(S/n);!_&&!p&&(p=!0),e[S]?(w++,S>0&&_>0&&e[S-1]||(l+=p?i("M",_+a,.5+h+a):i("m",c,0),c=0,p=!1),_+1<n&&e[S+1]||(l+=i("h",w),w=0)):c++}return l}return Xe.render=function(n,a,l){const c=s.getOptions(a),p=n.modules.size,w=n.modules.data,S=p+c.margin*2,_=c.color.light.a?"<path "+o(c.color.light,"fill")+' d="M0 0h'+S+"v"+S+'H0z"/>':"",h="<path "+o(c.color.dark,"stroke")+' d="'+t(w,p,c.margin)+'"/>',b='viewBox="0 0 '+S+" "+S+'"',j='<svg xmlns="http://www.w3.org/2000/svg" '+(c.width?'width="'+c.width+'" height="'+c.width+'" ':"")+b+' shape-rendering="crispEdges">'+_+h+`</svg>
`;return typeof l=="function"&&l(null,j),j},Xe}var Dt;function hn(){if(Dt)return pe;Dt=1;const s=Jt(),o=fn(),i=gn(),t=mn();function e(n,a,l,c,p){const w=[].slice.call(arguments,1),S=w.length,_=typeof w[S-1]=="function";if(!_&&!s())throw new Error("Callback required as last argument");if(_){if(S<2)throw new Error("Too few arguments provided");S===2?(p=l,l=a,a=c=void 0):S===3&&(a.getContext&&typeof p>"u"?(p=c,c=void 0):(p=c,c=l,l=a,a=void 0))}else{if(S<1)throw new Error("Too few arguments provided");return S===1?(l=a,a=c=void 0):S===2&&!a.getContext&&(c=l,l=a,a=void 0),new Promise(function(h,b){try{const v=o.create(l,c);h(n(v,a,c))}catch(v){b(v)}})}try{const h=o.create(l,c);p(null,n(h,a,c))}catch(h){p(h)}}return pe.create=o.create,pe.toCanvas=e.bind(null,i.render),pe.toDataURL=e.bind(null,i.renderToDataURL),pe.toString=e.bind(null,function(n,a,l){return t.render(n,l)}),pe}var pn=hn();const vn=Lt(pn);function yn(s={}){const o=V(null),i=V(!1),t=V(null),{showErrorAlert:e=!1,retryOnCsrfError:n=!0}=s,a=async h=>{i.value=!0,t.value=null;try{const b=await Z.get(h);return o.value=b.data,b.data}catch(b){const v=w(b);return t.value=v,e&&alert(v),null}finally{i.value=!1}},l=async(h,b={})=>{i.value=!0,t.value=null;try{const v=await Z.post(h,b);return o.value=v.data,v.data}catch(v){const j=w(v);return t.value=j,e&&!h.includes("/logout")&&alert(j),h.includes("/logout")?{message:"Logged out successfully"}:null}finally{i.value=!1}},c=async(h,b={})=>{i.value=!0,t.value=null;try{const v=await Z.put(h,b);return o.value=v.data,v.data}catch(v){const j=w(v);return t.value=j,e&&alert(j),null}finally{i.value=!1}},p=async h=>{i.value=!0,t.value=null;try{return await Z.delete(h),!0}catch(b){const v=w(b);return t.value=v,e&&alert(v),!1}finally{i.value=!1}},w=h=>{if(console.error("API Error:",h),h.response){const b=h.response.status,v=h.response.data;switch(b){case 401:return"Authentication required. Please log in again.";case 403:return"You do not have permission to perform this action.";case 404:return"The requested resource was not found.";case 419:return"Security token expired. Please refresh the page and try again.";case 422:if(v!=null&&v.errors){const j=Object.values(v.errors)[0];return Array.isArray(j)?j[0]:String(j)}return(v==null?void 0:v.message)||"Validation failed.";case 429:return"Too many requests. Please wait a moment and try again.";case 500:return"Server error. Please try again later.";default:return(v==null?void 0:v.message)||`Server error: ${b}`}}else return h.request?"Network error. Please check your connection and try again.":"An unexpected error occurred. Please try again."};return{data:o,loading:i,error:t,get:a,post:l,put:c,delete:p,reset:()=>{o.value=null,i.value=!1,t.value=null},clearError:()=>{t.value=null},handleError:w}}function wn(){const s=yn();return{...s,getReferralCode:()=>s.get("/referrals-code"),getReferralStats:()=>s.get("/referrals-my"),sendInvitation:n=>s.post("/send-referral-invite",{email:n}),getCreditBalance:()=>s.get("/credits-balance")}}const bn={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},xn={class:"flex items-center justify-between mb-6"},_n={class:"flex items-center space-x-3"},Cn={class:"text-sm text-gray-500"},kn={key:0,class:"text-center py-8"},En={key:1,class:"space-y-6"},Tn={class:"flex space-x-1 bg-gray-100 rounded-lg p-1"},Sn=["onClick"],An={key:0,class:"space-y-4"},Mn={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Dn={class:"flex items-center space-x-2"},In=["value"],Rn=["disabled"],Bn={class:"flex items-center space-x-2"},$n=["value"],Pn=["disabled"],Nn={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-center"},Ln={class:"text-center"},qn={class:"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg"},Fn={key:1,class:"space-y-4"},Un=["disabled"],jn={key:0,class:"flex items-center justify-center"},zn={key:1},Vn={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md"},Hn={key:1,class:"p-3 bg-red-100 border border-red-400 text-red-700 rounded-md"},On={key:2,class:"space-y-4"},Kn={class:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4"},Yn={class:"text-center"},Jn={class:"text-3xl font-bold text-purple-600"},Qn={class:"text-xs text-purple-600 mt-1"},Gn={class:"grid grid-cols-2 gap-4"},Wn={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-center"},Xn={class:"text-2xl font-bold text-blue-600"},Zn={class:"bg-green-50 border border-green-200 rounded-lg p-4 text-center"},eo={class:"text-2xl font-bold text-green-600"},to={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center"},no={class:"text-2xl font-bold text-yellow-600"},oo={key:0},ro={class:"space-y-3"},so={class:"flex items-center justify-between mb-2"},ao={class:"flex-1"},io={class:"text-sm font-medium text-gray-900"},lo={class:"text-xs text-gray-500"},co={class:"flex items-center justify-between text-xs"},uo={class:"text-gray-500"},fo={key:0,class:"text-green-600"},go={key:1,class:"text-center py-8"},mo={__name:"ReferralModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(s,{emit:o}){const i=s,t=o,e=wn(),n=V(!1),a=V("share"),l=V({}),c=V({}),p=V({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),w=V(""),S=V(!1),_=V(!1),h=V(""),b=V(""),v=V(null),j=[{id:"share",name:"Share"},{id:"invite",name:"Invite"},{id:"stats",name:"Stats"}],B=()=>{t("close")},q=async()=>{try{const m=await Z.get("/credits-balance");p.value=m.data}catch(m){console.error("Error loading credit balance:",m),p.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}}},I=async()=>{n.value=!0;try{console.log("Loading referral data..."),await q(),l.value={referral_code:"DEMO123",referral_url:"https://medroid.ai/register?ref=DEMO123"},c.value={total_count:5,completed_count:3,pending_count:2,total_earnings:15,referrals:[{id:1,email:"<EMAIL>",status:"completed",created_at:new Date().toISOString(),earnings:3},{id:2,email:"<EMAIL>",status:"pending",created_at:new Date().toISOString(),earnings:0}]};try{const m=await Z.get("/referrals-code");console.log("Referral code response:",m.data),m.data&&(l.value=m.data);const u=await Z.get("/referrals-my");console.log("Referral stats response:",u.data),u.data&&(c.value=u.data)}catch(m){console.log("API call failed, using demo data:",m.message)}await X(),l.value.referral_url&&(console.log("Calling generateQRCode from loadReferralData"),await R())}catch(m){console.error("Error in loadReferralData:",m)}finally{n.value=!1}},R=async()=>{if(console.log("generateQRCode called",{canvasExists:!!v.value,referralUrl:l.value.referral_url}),!l.value.referral_url){console.log("No referral URL available for QR code generation");return}if(await X(),!v.value){console.log("Canvas element not available, retrying..."),setTimeout(async()=>{await R()},100);return}try{const m=v.value;console.log("Canvas element found, generating QR code..."),m.getContext("2d").clearRect(0,0,m.width,m.height),m.width=128,m.height=128,await vn.toCanvas(m,l.value.referral_url,{width:128,margin:1,color:{dark:"#000000",light:"#FFFFFF"}}),console.log("QR code generated successfully")}catch(m){console.error("Error generating QR code:",m);const u=v.value;if(u){const f=u.getContext("2d");u.width=128,u.height=128,f.fillStyle="#f3f4f6",f.fillRect(0,0,128,128),f.fillStyle="#6b7280",f.font="12px Arial",f.textAlign="center",f.fillText("QR Code",64,64),f.fillText("Error",64,80)}}},J=async(m,u)=>{if(m)try{if(await navigator.clipboard.writeText(m),u&&u.target){const f=u.target.closest("button");if(f){const T=f.innerHTML;f.innerHTML='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',setTimeout(()=>{f.innerHTML=T},1e3)}}b.value="Copied to clipboard!",setTimeout(()=>{b.value=""},3e3)}catch(f){console.error("Failed to copy:",f);const T=document.createElement("textarea");T.value=m,document.body.appendChild(T),T.select();try{document.execCommand("copy"),b.value="Copied to clipboard!",setTimeout(()=>{b.value=""},3e3)}catch(P){console.error("Fallback copy failed:",P),b.value="Failed to copy",setTimeout(()=>{b.value=""},3e3)}document.body.removeChild(T)}},C=()=>{const m=`🤖 Meet your new AI Doctor!
Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.
Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}
Your pocket-sized doctor is here! 👩‍⚕️📱`,u=`https://wa.me/?text=${encodeURIComponent(m)}`;window.open(u,"_blank")},A=()=>{const m="Meet your new AI Doctor - Medroid",u=`🤖 Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}

Your pocket-sized doctor is here! 👩‍⚕️📱

Best regards!`,f=`mailto:?subject=${encodeURIComponent(m)}&body=${encodeURIComponent(u)}`;window.location.href=f},D=async m=>{if(m&&m.preventDefault(),!!w.value){S.value=!0,_.value=!1,h.value="";try{console.log("Sending invitation to:",w.value);const u=await e.sendInvitation(w.value);if(u){console.log("Invitation response:",u),_.value=!0,w.value="";try{const f=await e.getReferralStats();f&&(c.value=f),await q()}catch(f){console.log("Failed to reload stats:",f)}}else h.value=e.error.value||"Failed to send invitation. Please try again."}catch(u){console.error("Unexpected error sending invitation:",u),h.value="An unexpected error occurred. Please try again."}finally{S.value=!1}}},E=m=>{if(!m)return"";try{return new Date(m).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return m}};return Ae(()=>i.isOpen,async m=>{m&&(I(),setTimeout(async()=>{l.value.referral_url&&v.value&&v.value.getContext("2d").getImageData(0,0,v.value.width,v.value.height).data.every(P=>P===0)&&(console.log("Fallback QR code generation triggered - canvas is empty"),await R())},500))}),Ae(()=>l.value.referral_url,async m=>{m&&i.isOpen&&(console.log("Referral URL changed, regenerating QR code:",m),await X(),await R())}),Ae(a,()=>{_.value=!1,h.value="",b.value=""}),(m,u)=>s.isOpen?(L(),N("div",bn,[r("div",{class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity",onClick:Me(B,["self"])}),r("div",{class:"flex min-h-full items-center justify-center p-4",onClick:Me(B,["self"])},[r("div",{class:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",onClick:u[3]||(u[3]=Me(()=>{},["stop"]))},[r("div",xn,[r("div",_n,[u[5]||(u[5]=r("div",{class:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"},[r("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),r("div",null,[u[4]||(u[4]=r("h3",{class:"text-lg font-semibold text-gray-900"},"Refer & Earn",-1)),r("p",Cn," Total Balance: $"+O(parseFloat(p.value.balance||0).toFixed(2))+" | From Referrals: $"+O(parseFloat(p.value.referral_earnings||0).toFixed(2)),1)])]),r("button",{onClick:B,class:"text-gray-400 hover:text-gray-600"},u[6]||(u[6]=[r("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value?(L(),N("div",kn,u[7]||(u[7]=[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"},null,-1),r("p",{class:"text-gray-600"},"Loading your referral details...",-1)]))):(L(),N("div",En,[r("div",Tn,[(L(),N(ae,null,fe(j,f=>r("button",{key:f.id,onClick:T=>a.value=f.id,class:ve(["flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors",a.value===f.id?"bg-white text-green-600 shadow-sm":"text-gray-600 hover:text-gray-900"])},O(f.name),11,Sn)),64))]),a.value==="share"?(L(),N("div",An,[r("div",Mn,[u[9]||(u[9]=r("label",{class:"block text-sm font-medium text-green-800 mb-2"},"Your Referral Code",-1)),r("div",Dn,[r("input",{value:l.value.referral_code||"Loading...",readonly:"",class:ve(["flex-1 px-3 py-2 bg-white border border-green-300 rounded-md text-lg font-mono text-center",{"text-gray-400":!l.value.referral_code}])},null,10,In),r("button",{onClick:u[0]||(u[0]=f=>J(l.value.referral_code,f)),disabled:!l.value.referral_code,class:"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy code"},u[8]||(u[8]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,Rn)])]),r("div",null,[u[11]||(u[11]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Referral Link",-1)),r("div",Bn,[r("input",{value:l.value.referral_url||"Loading...",readonly:"",class:ve(["flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm",{"text-gray-400":!l.value.referral_url}])},null,10,$n),r("button",{onClick:u[1]||(u[1]=f=>J(l.value.referral_url,f)),disabled:!l.value.referral_url,class:"px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy link"},u[10]||(u[10]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,Pn)])]),b.value?(L(),N("div",Nn,O(b.value),1)):ee("",!0),r("div",Ln,[u[12]||(u[12]=r("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"QR Code",-1)),r("div",qn,[r("canvas",{ref_key:"qrCanvas",ref:v,class:"w-32 h-32"},null,512)]),u[13]||(u[13]=r("p",{class:"text-xs text-gray-500 mt-2"},"Share this QR code with friends",-1))]),r("div",{class:"grid grid-cols-2 gap-3"},[r("button",{onClick:C,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"},u[14]||(u[14]=[r("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},[r("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})],-1),r("span",{class:"text-sm font-medium"},"WhatsApp",-1)])),r("button",{onClick:A,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"},u[15]||(u[15]=[r("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1),r("span",{class:"text-sm font-medium"},"Email",-1)]))])])):ee("",!0),a.value==="invite"?(L(),N("div",Fn,[r("form",{onSubmit:D,class:"space-y-4"},[r("div",null,[u[16]||(u[16]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Friend's Email",-1)),Ze(r("input",{"onUpdate:modelValue":u[2]||(u[2]=f=>w.value=f),type:"email",required:"",placeholder:"Enter your friend's email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"},null,512),[[qt,w.value]])]),r("button",{type:"submit",disabled:S.value||!w.value,class:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[S.value?(L(),N("span",jn,u[17]||(u[17]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),It(" Sending... ")]))):(L(),N("span",zn,"Send Invitation"))],8,Un)],32),_.value?(L(),N("div",Vn," Invitation sent successfully! ")):ee("",!0),h.value?(L(),N("div",Hn,O(h.value),1)):ee("",!0)])):ee("",!0),a.value==="stats"?(L(),N("div",On,[r("div",Kn,[r("div",Yn,[r("div",Jn,"$"+O(parseFloat(p.value.balance||0).toFixed(2)),1),u[18]||(u[18]=r("div",{class:"text-sm text-purple-800 font-medium"},"Available Credit Balance",-1)),r("div",Qn," Total Earned: $"+O(parseFloat(p.value.total_earned||0).toFixed(2))+" | Used: $"+O(parseFloat(p.value.total_used||0).toFixed(2)),1)])]),r("div",Gn,[r("div",Wn,[r("div",Xn,O(c.value.total_count||0),1),u[19]||(u[19]=r("div",{class:"text-sm text-blue-800"},"Total Referrals",-1))]),r("div",Zn,[r("div",eo,O(c.value.completed_count||0),1),u[20]||(u[20]=r("div",{class:"text-sm text-green-800"},"Completed",-1))])]),r("div",to,[r("div",no,"$"+O(parseFloat(p.value.referral_earnings||0).toFixed(2)),1),u[21]||(u[21]=r("div",{class:"text-sm text-yellow-800"},"Referral Earnings",-1))]),c.value.referrals&&c.value.referrals.length>0?(L(),N("div",oo,[u[22]||(u[22]=r("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Recent Referrals",-1)),r("div",ro,[(L(!0),N(ae,null,fe(c.value.referrals.slice(0,5),f=>(L(),N("div",{key:f.id,class:"p-3 bg-gray-50 rounded-lg border"},[r("div",so,[r("div",ao,[r("div",io,O(f.email||"Anonymous Referral"),1),r("div",lo,O(E(f.created_at)),1)]),r("span",{class:ve(["text-xs px-2 py-1 rounded-full font-medium",f.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"])},O(f.status==="completed"?"Completed":"Pending"),3)]),r("div",co,[r("span",uo," Credit: $"+O(f.credit_amount||3),1),f.status==="completed"&&f.completed_at?(L(),N("span",fo," Completed "+O(E(f.completed_at)),1)):ee("",!0)])]))),128))])])):(L(),N("div",go,u[23]||(u[23]=[Ft('<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg></div><h4 class="text-sm font-medium text-gray-900 mb-1">No referrals yet</h4><p class="text-xs text-gray-500">Start sharing your referral code to earn rewards!</p>',3)])))])):ee("",!0)]))])])])):ee("",!0)}},ho={class:"h-full flex flex-col bg-gray-50 relative overflow-hidden"},po={class:"absolute top-4 left-4 z-20"},vo={class:"flex items-center space-x-3"},yo={key:0,class:"flex-1 flex flex-col items-center justify-center px-6 py-8"},wo={key:1,class:"flex-1 flex flex-col items-center justify-center px-6 py-8 pt-24"},bo={class:"text-center mb-12"},xo={class:"mx-auto mb-6"},_o={class:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-12 max-w-2xl"},Co=["onClick"],ko=["innerHTML"],Eo={class:"text-sm font-medium text-center"},To={key:2,class:"flex-1 overflow-hidden pt-24"},So={class:"h-full flex flex-col"},Ao={class:"max-w-4xl mx-auto px-4"},Mo={key:0,class:"flex items-start space-x-3 max-w-[85%] mb-2"},Do={class:"flex-shrink-0 mt-1"},Io={class:"flex-1 min-w-0"},Ro={key:0,class:"text-gray-900 leading-snug prose prose-sm max-w-none text-sm break-words"},Bo=["innerHTML"],$o=["innerHTML"],Po={key:2,class:"inline-flex items-center ml-1"},No={key:3,class:"mt-3 pt-3 border-t border-gray-200"},Lo={class:"flex items-center justify-between"},qo={class:"text-xs text-gray-500"},Fo=["href"],Uo={class:"text-xs text-gray-400 mt-0.5"},jo={key:1,class:"flex justify-end max-w-[85%] ml-auto mb-2"},zo={class:"bg-teal-500 text-white px-3 py-2 rounded-2xl rounded-br-md shadow-sm max-w-full"},Vo={class:"text-sm break-words whitespace-pre-wrap"},Ho={class:"text-xs text-teal-100 mt-0.5 text-right"},Oo={key:0,class:"max-w-4xl mx-auto px-4 mb-4"},Ko={class:"flex items-start space-x-3"},Yo={class:"flex-shrink-0 mt-1"},Jo={key:1,class:"max-w-4xl mx-auto px-4"},Qo={class:"flex items-start space-x-2"},Go={class:"flex-1 min-w-0"},Wo={class:"text-gray-800 leading-snug prose prose-sm max-w-none text-sm break-words"},Xo={class:"bg-white border border-gray-200 rounded-xl p-4 shadow-sm"},Zo={class:"mb-4"},er={class:"text-xs text-gray-500 mb-3"},tr={class:"flex flex-wrap gap-2 mb-3"},nr={class:"flex-1 min-w-0"},or=["value"],rr={class:"flex-1 min-w-0"},sr=["value"],ar={key:0,class:"flex items-center justify-center py-6"},ir={key:1,class:"text-center py-6"},lr={key:2},cr={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mb-3"},ur=["onClick"],dr={class:"flex items-center justify-between mb-1"},fr={class:"text-sm font-medium text-gray-900"},gr={class:"text-sm font-semibold text-teal-600"},mr={class:"text-xs text-gray-500 mb-1"},hr={key:0,class:"text-xs text-gray-400"},pr={key:0,class:"text-center pt-2"},vr={class:"mt-3 pt-3 border-t border-gray-200 flex items-center justify-between"},yr={class:"text-xs text-gray-400"},wr={class:"text-xs text-gray-400 mt-1"},br={class:"relative"},xr={class:"bg-gray-50 px-6 py-4 relative"},_r={class:"max-w-4xl mx-auto"},Cr={__name:"Chat",setup(s){const o=V([]),i=V(""),t=V(!1),e=V(null),n=V(null),a=V(null),l=V(!1),c=V([]),p=V([]),w=V(!1),S=V(!1),_=V(""),h=V("all"),b=V([]),v=V([]),j=V(!1),B=V(!1),q=V(null),I=V(null),R=[{title:"Chat",href:"/chat"}],J=[{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,text:"I have a headache",color:"bg-blue-50 text-blue-700 border-blue-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
    </svg>`,text:"My tummy hurts",color:"bg-green-50 text-green-700 border-green-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
    </svg>`,text:"I can't lose weight",color:"bg-purple-50 text-purple-700 border-purple-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
    </svg>`,text:"My urine burns",color:"bg-orange-50 text-orange-700 border-orange-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
    </svg>`,text:"Book appointment",color:"bg-teal-50 text-teal-700 border-teal-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z" />
    </svg>`,text:"I have a skin rash",color:"bg-indigo-50 text-indigo-700 border-indigo-200"}],C=x=>{if(!x)return x;let d=x;return d=d.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),d=d.replace(/^## (.*?):/gm,'<h2 class="font-bold text-gray-900 mt-4 mb-3 text-lg">$1:</h2>'),d=d.replace(/^### (.*?):/gm,'<h3 class="font-semibold text-gray-900 mt-3 mb-2">$1:</h3>'),d=d.replace(/^#### (.*?):/gm,'<h4 class="font-semibold text-gray-900 mt-2 mb-1 text-sm">$1:</h4>'),d=d.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g,'<div class="mb-2"><strong>$1. $2</strong></div>'),d=d.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs,'<div class="ml-4 text-gray-900 mb-2">Why I think so: $1</div>'),d=d.replace(/^• \*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/^- \*\*(.*?)\*\*/gm,'<div class="mb-2"><strong>• $1</strong></div>'),d=d.replace(/^- (.*$)/gm,'<div class="mb-1 ml-4">• $1</div>'),d=d.replace(/\*\*(.*?):\*\*/g,'<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>'),d=d.replace(/^\*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/\n\n/g,'</p><p class="mb-2">'),d=d.replace(/\n/g,"<br>"),d=`<p class="mb-2">${d}</p>`,d=d.replace(/<p class="mb-2"><\/p>/g,""),d=d.replace(/<br><br>/g,"<br>"),d},A=(x,d)=>{const g=o.value.find(F=>F.id===x);if(!g)return;g.isStreaming=!0,g.displayedContent="",g.streamingLines=[],q.value=x;const y=d.split(`
`).filter(F=>F.trim()!=="");let M=0;const $=()=>{if(M<y.length){const F=y[M];g.streamingLines.push({content:F,id:`line-${M}`,fadeIn:!0}),g.displayedContent=g.streamingLines.map(Q=>Q.content).join(`
`),M++,X(()=>Y());const U=F.length>100?800:500;I.value=setTimeout($,U)}else k()},k=()=>{g.isStreaming=!1,g.displayedContent=d,g.formatted=C(d),g.streamingLines=[],q.value=null,I.value&&(clearTimeout(I.value),I.value=null),X(()=>Y())};setTimeout(()=>{$()},200)},D=x=>{if(console.log("Processing backend slots:",x),!Array.isArray(x)){console.warn("backendSlots is not an array:",x),p.value=[],c.value=[];return}const d=[],g=new Date().toISOString().split("T")[0];x.forEach(y=>{if(!y||!y.provider){console.warn("Invalid provider slot:",y);return}const M=y.provider,$=y.service||null,k=y.date,F=y.day_of_week;y.slots&&Array.isArray(y.slots)&&y.slots.forEach(U=>{const Q={id:`${M.id}-${k}-${U.start_time}`,provider_id:M.id,service_id:($==null?void 0:$.id)||1,provider:`Dr. ${M.name}`,time:U.start_time,end_time:U.end_time,date:`${F}, ${new Date(k).toLocaleDateString()}`,full_date:k,price:($==null?void 0:$.price)||50,datetime:new Date(`${k} ${U.start_time}`),isToday:k===g};d.push(Q)})}),d.sort((y,M)=>y.isToday&&!M.isToday?-1:!y.isToday&&M.isToday?1:y.datetime-M.datetime),p.value=d,S.value=!1,E(d),ie(),console.log("Processed slots:",d.length,"Today slots:",Array.isArray(d)?d.filter(y=>y.isToday).length:0)},E=x=>{var $;if(!Array.isArray(x)){console.warn("slots is not an array in setupFilterOptions:",x),b.value=[{value:"",label:"All Days"}],v.value=[{value:"all",label:"All Providers"}],_.value="",h.value="all";return}const d=[...new Set(x.map(k=>k.full_date))],g=new Date().toISOString().split("T")[0];b.value=d.map(k=>{const F=new Date(k),U=k===g,Q=F.toDateString()===new Date(Date.now()+24*60*60*1e3).toDateString();let K=F.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"});return U?K=`Today (${K})`:Q&&(K=`Tomorrow (${K})`),{value:k,label:K}}).sort((k,F)=>new Date(k.value)-new Date(F.value)),b.value.push({value:"",label:"All Days"});const y=[...new Set(x.map(k=>({id:k.provider_id,name:k.provider})))];v.value=[{value:"all",label:"All Providers"},...y.map(k=>({value:k.id.toString(),label:k.name}))],b.value.find(k=>k.value===g)?_.value=g:_.value=(($=b.value[0])==null?void 0:$.value)||""},m=async()=>{var x;w.value=!0,await X(),Y();try{console.log("TEST: Loading providers...");const d=await Z.get("/providers"),g=d.data.data||d.data.providers||d.data||[];if(console.log("TEST: Providers loaded:",g.length),g.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.",timestamp:new Date});return}const y=new Date,M=[];let $=[];try{$=(await Z.get("/appointments-list")).data.appointments||[],console.log("Existing appointments loaded:",$.length)}catch{console.log("Could not load existing appointments, proceeding without conflict check")}const k=[];for(let H=0;H<=14;H++){const z=new Date(y);z.setDate(z.getDate()+H);const W=z.toISOString().split("T")[0];k.push({value:W,label:z.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}),fullDate:z})}b.value=k;const F=y.toISOString().split("T")[0];_.value||(_.value=F),v.value=[{value:"all",label:"All Providers"},...g.map(H=>{var z;return{value:H.id.toString(),label:`Dr. ${((z=H.user)==null?void 0:z.name)||"Provider"}`}})];for(let H=0;H<=14;H++){const z=new Date(y);z.setDate(z.getDate()+H);const W=z.toISOString().split("T")[0];console.log(`TEST: Checking date ${W}`);for(const G of g){console.log(`TEST: Checking provider ${G.id} - ${(x=G.user)==null?void 0:x.name}`);try{const ne=(await Z.get(`/providers/${G.id}/available-slots?date=${W}`)).data.available_slots||[];if(console.log(`TEST: Provider ${G.id} has ${ne.length} slots on ${W}`),ne.length>0){const be=ne.filter(se=>{const ce=`${W} ${se.start_time}`;return!$.some(xe=>{var tt;return xe.status==="cancelled"?!1:`${xe.date||((tt=xe.scheduled_at)==null?void 0:tt.split("T")[0])} ${xe.time||xe.start_time}`===ce})});if(console.log(`TEST: Provider ${G.id} has ${be.length} available slots after filtering`),be.length===0){console.log(`TEST: All slots for provider ${G.id} on ${W} are already booked`);continue}let te=null;try{const ce=(await Z.get(`/providers/${G.id}/services`)).data.services||[];console.log(`TEST: Services for provider ${G.id}:`,ce),ce.length>0&&(te=ce[0],console.log("TEST: Selected service:",te),console.log(`TEST: Service ID: ${te.id}, Name: ${te.name}, Price: ${te.price}`))}catch(se){console.error(`Error loading services for provider ${G.id}:`,se)}if(!te||!te.id){console.error(`No valid service found for provider ${G.id}, skipping slots`);continue}const Nt=be.map(se=>{var ce;return{id:`${G.id}-${W}-${se.start_time}`,provider_id:G.id,service_id:te.id,provider:`Dr. ${((ce=G.user)==null?void 0:ce.name)||"Provider"}`,specialty:G.specialization||"General Practice",date:z.toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"}),time:se.start_time,end_time:se.end_time,full_date:W,datetime:new Date(`${W}T${se.start_time}`),price:te.price||0,duration:te.duration||15,service_name:te.name||"Consultation"}});M.push(...Nt)}}catch(le){console.error(`Error loading slots for provider ${G.id} on ${W}:`,le)}}if(M.length>=50)break}if(console.log("TEST: Total slots found:",M.length),M.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.",timestamp:new Date});return}M.sort((H,z)=>H.datetime-z.datetime);const U={};M.forEach(H=>{U[H.provider_id]||(U[H.provider_id]=[]),U[H.provider_id].push(H)});const Q=[],K=Math.ceil(8/Object.keys(U).length);Object.keys(U).forEach(H=>{const z=U[H].slice(0,K);Q.push(...z)}),Q.sort((H,z)=>H.datetime-z.datetime),p.value=Q,S.value=!1,l.value=!0,ie(),await X(),Y()}catch(d){console.error("Error loading appointment slots:",d),o.value.push({id:Date.now(),type:"ai",content:"I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.",timestamp:new Date})}finally{w.value=!1,await X(),Y()}},u=async x=>{var d,g,y,M,$;try{let k="Appointment booked through AI chat consultation",F="General consultation";if(a.value)try{const z=await Z.get(`/web-api/chat/conversation/${a.value}`);if(z.data&&z.data.referral_note){k=z.data.referral_note;const W=k.replace(/\*\*/g,"").replace(/###/g,"").replace(/\n+/g," ").trim();F=W.length>150?W.substring(0,150)+"...":W}}catch{console.log("Could not fetch referral note, using default reason")}let U=x.service_id;if(x.provider_id===3&&(U=4,console.log("Fixed service ID for Provider 3 to Service ID 4")),!U)throw new Error("Invalid appointment slot: missing service ID");console.log("Using service ID:",U,"for provider:",x.provider_id);const Q={provider_id:x.provider_id,service_id:U,date:x.full_date,time_slot:{start_time:x.time,end_time:x.end_time},reason:F,notes:k,currency:"USD"};console.log("Booking appointment with data (UPDATED):",Q),console.log("Slot data:",x),console.log("Service ID being sent:",Q.service_id),console.log("Provider ID being sent:",Q.provider_id);const K=await fetch("/api/appointments/with-payment",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(Q)});if(!K.ok){const z=await K.json();throw new Error(z.message||`HTTP ${K.status}`)}const H=await K.json();if(H.appointment){const z=H.appointment,W=H.payment;if(W&&W.client_secret){const G=`🏥 Appointment Created - Payment Required

Provider: ${x.provider}
Date: ${x.date}
Time: ${x.time}
Appointment ID: #${z.id}
Amount: $${W.amount}

Your appointment has been created and is pending payment. Please click the "Pay Now" button below to complete your booking.`,le={id:Date.now(),type:"ai",content:G,timestamp:new Date,formatted:C(G),showPaymentButton:!0,appointmentId:z.id,paymentAmount:W.amount,paymentUrl:`/appointments/${z.id}/payment?conversation=${a.value}`};if(o.value.push(le),a.value)try{await Z.post("/web-api/chat/message",{conversation_id:a.value,message:le.content,role:"assistant"}),console.log("Payment message saved to chat history")}catch(ne){console.error("Error saving payment message to chat history:",ne)}setTimeout(async()=>{const ne={id:Date.now()+1,type:"ai",content:"While you complete your payment, I'll be here if you have any questions about your upcoming appointment or any other health concerns. Feel free to ask me anything! 😊",timestamp:new Date,formatted:C("While you complete your payment, I'll be here if you have any questions about your upcoming appointment or any other health concerns. Feel free to ask me anything! 😊")};if(o.value.push(ne),a.value)try{await Z.post("/api/chat/message",{conversation_id:a.value,message:ne.content,role:"assistant"})}catch(be){console.error("Error saving follow-up message to chat history:",be)}await X(),Y()},3e3)}else{const G=`✅ **Appointment Confirmed!**

**Provider:** ${x.provider}
**Date:** ${x.date}
**Time:** ${x.time}
**Appointment ID:** #${z.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`,le={id:Date.now(),type:"ai",content:G,timestamp:new Date,formatted:C(G)};if(o.value.push(le),a.value)try{await Z.post("/web-api/chat/message",{conversation_id:a.value,message:le.content,role:"assistant"}),console.log("Confirmation message saved to chat history")}catch(ne){console.error("Error saving confirmation message to chat history:",ne)}}}else throw new Error("No appointment data returned");l.value=!1,await X(),Y()}catch(k){console.error("Error booking appointment:",k);let F="Unknown error occurred";(y=(g=k.response)==null?void 0:g.data)!=null&&y.message?F=k.response.data.message:($=(M=k.response)==null?void 0:M.data)!=null&&$.errors?F=Object.values(k.response.data.errors).flat().join(", "):k.message&&(F=k.message),console.log("Detailed error:",F);const U={id:Date.now(),type:"ai",content:`I apologize, but there was an error booking your appointment with ${x.provider}.

**Error Details:** ${F}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,timestamp:new Date,formatted:C(`I apologize, but there was an error booking your appointment with ${x.provider}.

**Error Details:** ${F}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)};if(o.value.push(U),a.value)try{await Z.post("/web-api/chat/message",{conversation_id:a.value,message:U.content,role:"assistant"})}catch(Q){console.error("Error saving error message to chat history:",Q)}l.value=!1,await X(),Y()}},f=async(x=null)=>{var g,y;const d=x||i.value.trim();if(d){o.value.push({id:Date.now(),type:"user",content:d,timestamp:new Date}),x||(i.value=""),await X(),Y(),t.value=!0;try{if(!a.value){const F=await fetch("/web-api/chat/start",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((g=document.querySelector('meta[name="csrf-token"]'))==null?void 0:g.getAttribute("content"))||""},credentials:"include",body:JSON.stringify({})}),U=await F.json();if(!F.ok)throw console.error("Start conversation failed:",U),new Error(U.message||"Failed to start conversation");if(!U.conversation_id)throw new Error("No conversation ID returned");a.value=String(U.conversation_id)}const M={conversation_id:String(a.value),message:d,include_patient_context:!0,generate_title:!0,request_full_response:!1};console.log("Sending payload:",M);const $=await fetch("/web-api/chat/message",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(M)}),k=await $.json();if($.ok&&k.message){const F=Date.now()+1,U={id:F,type:"ai",content:k.message,timestamp:new Date,formatted:C(k.message),isStreaming:!1,displayedContent:""};if(o.value.push(U),await X(),Y(),A(F,k.message),k.available_slots&&Array.isArray(k.available_slots))console.log("Backend provided appointment slots directly:",k.available_slots),D(k.available_slots),setTimeout(()=>{l.value=!0,X(()=>{Y()})},500);else if(k.appointment_options||k.show_appointments)setTimeout(()=>{m()},1e3);else{const Q=k.message.toLowerCase();["would you like to see available appointment slots","shall i show you available appointments","would you like to book an appointment now","let me show you available slots","here are the available appointment times","perfect! i found available appointment slots","appointment options provided"].some(z=>Q.includes(z))&&setTimeout(()=>{m()},1e3)}}else{console.error("Failed to get AI response:",k);const F=Date.now()+1,U={id:F,type:"ai",content:`Sorry, I encountered an error: ${k.message||"Please try again."}`,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push(U),await X(),A(F,U.content)}}catch(M){console.error("An error occurred while sending your message:",M);const $=Date.now()+1,k={id:$,type:"ai",content:`Sorry, I encountered an error: ${M.message||"Please try again."}`,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push(k),await X(),A($,k.content)}finally{t.value=!1,await X(),Y(),n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)}}},T=x=>{f(x.text)},P=x=>{x.key==="Enter"&&!x.shiftKey&&(x.preventDefault(),f())},Y=()=>{e.value&&(e.value.scrollTop=e.value.scrollHeight)},re=x=>new Date(x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),Te=()=>{o.value=[],a.value=null,l.value=!1,i.value="",n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)},Ce=()=>{B.value=!0},Se=()=>{B.value=!1},ie=()=>{if(!Array.isArray(p.value)){console.warn("allAvailableSlots.value is not an array:",p.value),p.value=[],c.value=[];return}let x=[...p.value];_.value&&_.value!==""&&_.value!=="all"&&(x=x.filter(d=>d.full_date===_.value)),h.value&&h.value!=="all"&&(x=x.filter(d=>d.provider_id.toString()===h.value)),c.value=x,S.value=!0},he=async x=>{if(x){j.value=!0;try{const g=(await Z.get(`/web-api/chat/conversation/${x}`)).data;g&&g.id&&(a.value=x,g.messages&&Array.isArray(g.messages)&&(o.value=g.messages.map(y=>({id:y._id||y.id||Date.now()+Math.random(),type:y.role==="user"?"user":"ai",content:y.content||y.message,timestamp:new Date(y.timestamp||y.created_at||Date.now()),formatted:y.role!=="user"?C(y.content||y.message):void 0})),await X(),Y()))}catch(d){console.error("Error loading conversation:",d),a.value=null,o.value=[]}finally{j.value=!1}}},ye=async()=>{var x,d,g;try{const y=localStorage.getItem("anonymous_conversation");if(!y)return null;const M=JSON.parse(y);if(!M.conversation_id||!M.anonymous_id)return null;console.log("Transferring anonymous conversation:",M.conversation_id),await new Promise(F=>setTimeout(F,500));const $={conversation_id:String(M.conversation_id),anonymous_id:String(M.anonymous_id)};if(console.log("Transfer payload:",$),(await Z.post("/web-api/chat/transfer-anonymous",$,{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((x=document.querySelector('meta[name="csrf-token"]'))==null?void 0:x.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})).data.success)return console.log("Anonymous conversation transferred successfully"),localStorage.removeItem("anonymous_conversation"),M.conversation_id}catch(y){if(console.error("Error transferring anonymous conversation:",y),y.response&&(console.error("Response status:",y.response.status),console.error("Response data:",y.response.data)),((d=y.response)==null?void 0:d.status)===401)return console.log("Authentication required for transfer, will retry later"),null;if(((g=y.response)==null?void 0:g.status)===422)return console.error("Validation errors:",y.response.data.errors),null;localStorage.removeItem("anonymous_conversation")}return null},we=async x=>{var d,g,y;try{const $=(await Z.get(`/api/appointments/${x}`)).data.appointment,k=K=>{try{return new Date(K).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})}catch{return K||"Date not available"}},U=`🎉 **Payment Successful - Appointment Confirmed!**

Excellent! Your payment has been processed successfully and your appointment is now confirmed.

**📅 Appointment Details:**
**Provider:** ${(K=>{var H;return(H=K==null?void 0:K.user)!=null&&H.name?K.user.name:K!=null&&K.name?`Dr. ${K.name}`:"Your Doctor"})($.provider)}
**Date:** ${k($.date)}
**Time:** ${((d=$.time_slot)==null?void 0:d.start_time)||"Time TBD"} - ${((g=$.time_slot)==null?void 0:g.end_time)||"Time TBD"}
**Service:** ${((y=$.service)==null?void 0:y.name)||"Consultation"}
**Amount Paid:** $${$.amount||"0.00"}
**Appointment ID:** #${$.id||"N/A"}

**📧 What's Next:**
• You'll receive a confirmation email shortly
• A calendar invite will be sent to your email
• You can view and manage this appointment in your appointments section

Is there anything else I can help you with regarding your upcoming appointment or any other health concerns?`,Q={id:Date.now(),type:"ai",content:U,timestamp:new Date,formatted:C(U)};if(o.value.push(Q),a.value)try{await Z.post("/web-api/chat/message",{conversation_id:a.value,message:Q.content,role:"assistant"}),console.log("Payment confirmation message saved to chat history")}catch(K){console.error("Error saving confirmation message to chat history:",K)}await X(),Y()}catch(M){console.error("Error handling payment success:",M);const $=`🎉 **Payment Successful!**

Great news! Your payment has been processed successfully and your appointment is confirmed.

**Appointment ID:** #${x}

You'll receive a confirmation email shortly with all the details. You can also view your appointment in the appointments section.

Is there anything else I can help you with today?`,k={id:Date.now(),type:"ai",content:$,timestamp:new Date,formatted:C($)};o.value.push(k),await X(),Y()}};return Ut(async()=>{const x=new URLSearchParams(window.location.search),d=x.get("conversation"),g=x.get("payment_success"),y=x.get("appointment_id");let M=await ye();!M&&localStorage.getItem("anonymous_conversation")&&(console.log("Retrying anonymous conversation transfer after authentication delay..."),await new Promise(k=>setTimeout(k,1500)),M=await ye());const $=d||M;$&&he($),g==="true"&&y&&(await we(y),window.history.replaceState({},document.title,window.location.pathname))}),jt(()=>{I.value&&clearTimeout(I.value)}),(x,d)=>(L(),N(ae,null,[ue(zt(Vt),{title:"Chat - Medroid"}),ue(Ot,{breadcrumbs:R},{default:Ht(()=>[r("div",ho,[r("div",po,[r("div",vo,[ue(ke,{size:32}),d[5]||(d[5]=r("div",null,[r("h1",{class:"text-sm font-semibold text-gray-900"},"Medroid AI"),r("p",{class:"text-xs text-gray-500"},"Your AI Doctor")],-1))])]),r("div",{class:"absolute top-4 right-4 z-20"},[r("div",{class:"flex items-center space-x-3"},[r("button",{onClick:Te,class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm border border-white/50 rounded-full hover:bg-white/90 transition-all duration-200 shadow-lg"},d[6]||(d[6]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),r("span",null,"New Chat",-1)])),r("button",{onClick:Ce,class:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 bg-green-50/80 backdrop-blur-sm border border-green-200/50 rounded-full hover:bg-green-100/80 transition-all duration-200 shadow-lg"},d[7]||(d[7]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),r("span",null,"Refer & Earn",-1)]))])]),j.value?(L(),N("div",yo,d[8]||(d[8]=[r("div",{class:"text-center"},[r("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),r("p",{class:"text-gray-600"},"Loading conversation...")],-1)]))):o.value.length===0?(L(),N("div",wo,[r("div",bo,[r("div",xo,[ue(ke,{size:80,"show-shadow":!0})]),d[9]||(d[9]=r("h1",{class:"text-4xl font-bold text-gray-900 mb-4"},"Welcome to Medroid",-1)),d[10]||(d[10]=r("p",{class:"text-lg text-gray-600 max-w-md mx-auto"}," Your personal AI Dr. Ask me anything about your symptoms. ",-1))]),r("div",_o,[(L(),N(ae,null,fe(J,g=>r("button",{key:g.text,onClick:y=>T(g),class:ve([g.color,"flex flex-col items-center p-4 rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105"])},[r("div",{class:"mb-2",innerHTML:g.icon},null,8,ko),r("span",Eo,O(g.text),1)],10,Co)),64))])])):(L(),N("div",To,[r("div",So,[r("div",{ref_key:"chatContainer",ref:e,class:"flex-1 overflow-y-auto pt-4 pb-2 space-y-6"},[r("div",Ao,[(L(!0),N(ae,null,fe(o.value,g=>(L(),N("div",{key:g.id,class:ve(["flex mb-4",g.type==="user"?"justify-end":"justify-start"])},[g.type==="ai"?(L(),N("div",Mo,[r("div",Do,[ue(ke,{size:24})]),r("div",Io,[g.isStreaming&&g.streamingLines?(L(),N("div",Ro,[(L(!0),N(ae,null,fe(g.streamingLines,y=>(L(),N("div",{key:y.id,class:"animate-fade-in-line",innerHTML:C(y.content)},null,8,Bo))),128))])):(L(),N("div",{key:1,class:"text-gray-900 leading-snug prose prose-sm max-w-none text-sm break-words",innerHTML:g.formatted||g.content},null,8,$o)),g.isStreaming?(L(),N("span",Po,d[11]||(d[11]=[r("span",{class:"w-0.5 h-4 bg-gradient-to-t from-teal-500 to-teal-300 animate-futuristic-pulse rounded-full shadow-sm"},null,-1),r("span",{class:"ml-1 flex space-x-0.5"},[r("span",{class:"w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm",style:{"animation-delay":"0ms"}}),r("span",{class:"w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm",style:{"animation-delay":"150ms"}}),r("span",{class:"w-1 h-1 bg-teal-500 rounded-full animate-modern-bounce shadow-sm",style:{"animation-delay":"300ms"}})],-1)]))):ee("",!0),g.showPaymentButton?(L(),N("div",No,[r("div",Lo,[r("div",qo," Appointment #"+O(g.appointmentId)+" • $"+O(g.paymentAmount),1),r("a",{href:g.paymentUrl,class:"inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},d[12]||(d[12]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),It(" Pay Now ")]),8,Fo)])])):ee("",!0),r("div",Uo,O(re(g.timestamp)),1)])])):(L(),N("div",jo,[r("div",zo,[r("div",Vo,O(g.content),1),r("div",Ho,O(re(g.timestamp)),1)])]))],2))),128))]),t.value?(L(),N("div",Oo,[r("div",Ko,[r("div",Yo,[ue(ke,{size:24})]),d[13]||(d[13]=r("div",{class:"flex-1"},[r("div",{class:"flex items-center space-x-1"},[r("span",{class:"text-sm text-gray-600"},"AI is thinking"),r("div",{class:"flex space-x-1 ml-2"},[r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce"}),r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.15s"}}),r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.3s"}})])])],-1))])])):ee("",!0),l.value?(L(),N("div",Jo,[r("div",Qo,[d[18]||(d[18]=r("div",{class:"w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1"},[r("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),r("div",Go,[r("div",Wo,[r("div",Xo,[r("div",Zo,[d[15]||(d[15]=r("h3",{class:"text-sm font-medium text-gray-900 mb-2"},"Available Appointments",-1)),r("p",er,O(c.value.length)+" slots available • Click any slot to book ",1),r("div",tr,[r("div",nr,[Ze(r("select",{"onUpdate:modelValue":d[0]||(d[0]=g=>_.value=g),onChange:ie,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[d[14]||(d[14]=r("option",{value:""},"All Days",-1)),(L(!0),N(ae,null,fe(b.value,g=>(L(),N("option",{key:g.value,value:g.value},O(g.label),9,or))),128))],544),[[nt,_.value]])]),r("div",rr,[Ze(r("select",{"onUpdate:modelValue":d[1]||(d[1]=g=>h.value=g),onChange:ie,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[(L(!0),N(ae,null,fe(v.value,g=>(L(),N("option",{key:g.value,value:g.value},O(g.label),9,sr))),128))],544),[[nt,h.value]])])])]),w.value?(L(),N("div",ar,d[16]||(d[16]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-500"},null,-1),r("span",{class:"ml-2 text-xs text-gray-600"},"Loading slots...",-1)]))):c.value.length===0?(L(),N("div",ir,d[17]||(d[17]=[r("p",{class:"text-xs text-gray-500"},"No appointments available for selected filters",-1)]))):(L(),N("div",lr,[r("div",cr,[(L(!0),N(ae,null,fe(S.value?c.value:c.value.slice(0,9),g=>(L(),N("button",{key:g.id,onClick:y=>u(g),class:"flex flex-col p-3 bg-gray-50 hover:bg-teal-50 border border-gray-200 hover:border-teal-300 rounded-lg transition-all duration-200 text-left"},[r("div",dr,[r("div",fr,O(g.time),1),r("div",gr," $"+O(g.price),1)]),r("div",mr,O(g.provider.replace("Dr. ","")),1),_.value===""?(L(),N("div",hr,O(g.date.split(",")[0]),1)):ee("",!0)],8,ur))),128))]),c.value.length>9?(L(),N("div",pr,[r("button",{onClick:d[2]||(d[2]=g=>S.value=!S.value),class:"text-xs text-teal-600 hover:text-teal-700 font-medium px-3 py-1 rounded-md hover:bg-teal-50 transition-colors"},O(S.value?"Show less":`Show ${c.value.length-9} more slots`),1)])):ee("",!0)])),r("div",vr,[r("button",{onClick:d[3]||(d[3]=g=>l.value=!1),class:"text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"}," Close "),r("div",yr,O(_.value?"Filtered by date":"All available days"),1)])])]),r("div",wr,O(re(new Date)),1)])])])):ee("",!0)],512)])])),r("div",br,[d[19]||(d[19]=r("div",{class:"absolute inset-x-0 bottom-0 h-24 bg-gray-50 -z-10"},null,-1)),r("div",xr,[r("div",_r,[ue(Kt,{ref_key:"chatInputRef",ref:n,modelValue:i.value,"onUpdate:modelValue":d[4]||(d[4]=g=>i.value=g),placeholder:"Type your health question...","is-loading":t.value,"show-tools":!1,"show-version":!1,onSend:f,onKeydown:P},null,8,["modelValue","is-loading"])])])])]),ue(mo,{"is-open":B.value,onClose:Se},null,8,["is-open"])]),_:1})],64))}},Rr=Yt(Cr,[["__scopeId","data-v-1972c6a0"]]);export{Rr as default};
