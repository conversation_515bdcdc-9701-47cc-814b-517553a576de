<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Permission groups with their associated permissions.
     *
     * @var array
     */
    protected $permissionGroups = [
        'users' => ['view', 'create', 'edit', 'delete'],
        'providers' => ['view', 'create', 'edit', 'delete'],
        'patients' => ['view', 'create', 'edit', 'delete'],
        'clinics' => ['view', 'create', 'edit', 'delete', 'manage'],
        'appointments' => ['view', 'create', 'edit', 'delete'],
        'payments' => ['view', 'create', 'edit', 'delete'],
        'chats' => ['view', 'create', 'edit', 'delete'],
        'services' => ['view', 'create', 'edit', 'delete'],
        'products' => ['view', 'create', 'edit', 'delete', 'manage'],
        'orders' => ['view', 'create', 'edit', 'delete', 'manage'],
        'reports' => ['view', 'create', 'export'],
        'settings' => ['view', 'edit'],
        'features' => ['view', 'create', 'edit', 'delete'],
        'analytics' => ['view', 'export'],
        'diagnostic feedback' => ['view', 'create', 'edit'],
        'permissions' => ['view', 'create', 'edit', 'delete', 'manage'],
        'email templates' => ['view', 'create', 'edit', 'delete', 'manage'],
        'notifications' => ['view', 'create', 'edit', 'delete', 'manage'],
        'referrals' => ['view', 'create', 'edit', 'delete', 'manage'],
        'credits' => ['view', 'create', 'edit', 'delete', 'manage'],
        'clubs' => ['view', 'create', 'edit', 'delete', 'manage'],
    ];

    /**
     * Role definitions with their associated permissions.
     *
     * @var array
     */
    protected $roleDefinitions = [
        'admin' => [
            'description' => 'Full system access',
            'permissions' => 'all',
        ],
        'manager' => [
            'description' => 'Management access with limited deletion rights',
            'permissions' => [
                'users' => ['view', 'edit'],
                'providers' => ['view', 'edit'],
                'patients' => ['view', 'edit'],
                'clinics' => ['view', 'edit'],
                'appointments' => ['view', 'edit'],
                'payments' => ['view', 'edit'],
                'chats' => ['view', 'edit'],
                'services' => ['view', 'edit'],
                'products' => ['view', 'edit'],
                'orders' => ['view', 'edit'],
                'reports' => ['view', 'create', 'export'],
                'settings' => ['view', 'edit'],
                'analytics' => ['view', 'export'],
                'diagnostic feedback' => ['view'],
            ],
        ],
        'provider' => [
            'description' => 'Healthcare provider access',
            'permissions' => [
                'appointments' => ['view', 'edit'],
                'patients' => ['view'],
                'payments' => ['view'],
                'chats' => ['view', 'create'],
                'services' => ['view', 'edit'],
                'reports' => ['view'],
                'diagnostic feedback' => ['view', 'create', 'edit'],
            ],
        ],
        'patient' => [
            'description' => 'Patient access',
            'permissions' => [
                'appointments' => ['view', 'create'],
                'providers' => ['view'],
                'payments' => ['view', 'create'],
                'chats' => ['view', 'create'],
                'services' => ['view'],
            ],
        ],
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Disable foreign key checks
        Schema::disableForeignKeyConstraints();

        // Clear existing roles and permissions
        $this->clearRolesAndPermissions();

        // Re-enable foreign key checks
        Schema::enableForeignKeyConstraints();

        // Reset cached roles and permissions
        try {
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        } catch (\Exception $e) {
            // Ignore cache clearing errors when using file-based cache
            // This is expected when cache tables don't exist
        }

        // Create all permissions
        $this->createPermissions();

        // Create roles and assign permissions
        $this->createRolesWithPermissions();
    }

    /**
     * Clear existing roles and permissions.
     *
     * @return void
     */
    protected function clearRolesAndPermissions()
    {
        // Get table names from config
        $tableNames = config('permission.table_names');

        if (!empty($tableNames)) {
            // Truncate pivot tables first
            DB::table($tableNames['role_has_permissions'])->truncate();
            DB::table($tableNames['model_has_roles'])->truncate();
            DB::table($tableNames['model_has_permissions'])->truncate();

            // Then truncate the main tables
            DB::table($tableNames['roles'])->truncate();
            DB::table($tableNames['permissions'])->truncate();
        }
    }

    /**
     * Create all permissions based on the permission groups.
     *
     * @return void
     */
    protected function createPermissions()
    {
        $allPermissions = [];

        // Generate all permissions from the groups
        foreach ($this->permissionGroups as $group => $actions) {
            foreach ($actions as $action) {
                $permissionName = "{$action} {$group}";
                $allPermissions[] = $permissionName;

                Permission::create([
                    'name' => $permissionName,
                    'guard_name' => 'web',
                ]);
            }
        }

        $this->command->info('Created ' . count($allPermissions) . ' permissions.');
    }

    /**
     * Create roles and assign permissions to them.
     *
     * @return void
     */
    protected function createRolesWithPermissions()
    {
        // Get all available permissions
        $allPermissions = Permission::all();

        foreach ($this->roleDefinitions as $roleName => $roleData) {
            // Create the role
            $role = Role::create([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);

            // Assign permissions to the role
            if ($roleData['permissions'] === 'all') {
                // Assign all permissions to admin
                $role->syncPermissions($allPermissions);
            } else {
                $rolePermissions = [];

                // Process the permissions for this role
                foreach ($roleData['permissions'] as $group => $actions) {
                    foreach ($actions as $action) {
                        $rolePermissions[] = "{$action} {$group}";
                    }
                }

                $role->syncPermissions($rolePermissions);
            }

            $this->command->info("Created role '{$roleName}' with " . count($role->permissions) . " permissions.");
        }
    }
}
