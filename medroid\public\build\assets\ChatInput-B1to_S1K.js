import{N as x,r as _,w as k,C as n,d,e as i,i as t,A as V,x as u}from"./vendor-DwpQ5WHX.js";import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={class:"space-y-3"},B={class:"relative"},I={class:"bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"},H={class:"relative"},T=["value","placeholder","disabled"],z={class:"flex items-center justify-end px-4 pb-3"},M=["disabled"],A={key:0,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24"},N={key:1,class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},R=x({__name:"ChatInput",props:{modelValue:{},placeholder:{default:"Type your message..."},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1},showTools:{type:Boolean,default:!1},showVersion:{type:Boolean,default:!1}},emits:["update:modelValue","send","keydown"],setup(p,{expose:h,emit:f}){const g=p,r=f,a=_(null),y=e=>e&&e.replace(/([.!?]\s*)([a-z])/g,(o,s,l)=>s+l.toUpperCase()).replace(/^[a-z]/,o=>o.toUpperCase()),c=()=>{if(a.value){a.value.style.height="auto";const e=a.value.scrollHeight,o=100,l=Math.min(Math.max(e,40),o);a.value.style.height=`${l}px`,e>o?a.value.style.overflowY="auto":a.value.style.overflowY="hidden"}},v=e=>{const o=e.target,s=y(o.value);r("update:modelValue",s),n(()=>{c()})},m=e=>{r("keydown",e)},w=()=>{r("send"),n(()=>{a.value&&(a.value.style.height="40px",a.value.style.overflowY="hidden")})},b=()=>{a.value&&a.value.focus()};return k(()=>g.modelValue,()=>{n(()=>{c()})}),h({focus:b}),(e,o)=>(i(),d("div",L,[t("div",B,[t("div",I,[t("div",H,[t("textarea",{ref_key:"chatInputRef",ref:a,value:e.modelValue,onInput:v,onKeydown:m,placeholder:e.placeholder,class:"w-full px-4 py-3 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed transition-all duration-200 break-words",style:{height:"40px","overflow-y":"hidden","word-wrap":"break-word","white-space":"pre-wrap"},disabled:e.disabled||e.isLoading},null,40,T),t("div",z,[t("button",{onClick:w,disabled:e.isLoading||!e.modelValue.trim(),class:V(["p-2 rounded-lg transition-all duration-200 transform hover:scale-105",e.modelValue.trim()&&!e.isLoading?"bg-teal-500 text-white hover:bg-teal-600 shadow-lg":"bg-gray-200 text-gray-500 cursor-not-allowed"])},[e.isLoading?(i(),d("div",N)):(i(),d("svg",A,o[0]||(o[0]=[t("path",{d:"M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z"},null,-1)])))],10,M)])])])]),o[1]||(o[1]=t("div",{class:"relative"},[t("p",{class:"text-xs text-gray-500 text-center leading-relaxed px-4 py-2 bg-gray-50/60 rounded-lg border border-gray-200/50"},[u(" Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our "),t("a",{href:"https://medroid.ai/terms-of-service-1/",target:"_blank",class:"text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150"},"Terms of Service"),u(" and "),t("a",{href:"https://medroid.ai/privacy-policy/",target:"_blank",class:"text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150"},"Privacy Policy"),u(". ")])],-1))]))}}),K=C(R,[["__scopeId","data-v-c313781d"]]);export{K as C};
