<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'slug',
        'description',
        'short_description',
        'type',
        'category_id',
        'price',
        'sale_price',
        'sku',
        'stock_quantity',
        'manage_stock',
        'in_stock',
        'weight',
        'dimensions',
        'attributes',
        'featured_image',
        'gallery_images',
        'is_featured',
        'is_active',
        'sort_order',
        'digital_files',
        'download_limit',
        'download_expiry_days',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'stock_quantity' => 'integer',
        'manage_stock' => 'boolean',
        'in_stock' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'download_limit' => 'integer',
        'download_expiry_days' => 'integer',
        'attributes' => 'array',
        'gallery_images' => 'array',
        'digital_files' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort_order');
    }

    public function cartItems()
    {
        return $this->hasMany(ShoppingCart::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function digitalDownloads()
    {
        return $this->hasMany(DigitalProductDownload::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeInStock($query)
    {
        return $query->where('in_stock', true);
    }

    public function scopePhysical($query)
    {
        return $query->where('type', 'physical');
    }

    public function scopeDigital($query)
    {
        return $query->where('type', 'digital');
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeOwnedBy($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function getEffectivePriceAttribute()
    {
        return $this->sale_price ?? $this->price;
    }

    public function getIsOnSaleAttribute()
    {
        return !is_null($this->sale_price) && $this->sale_price < $this->price;
    }

    public function getDiscountPercentageAttribute()
    {
        if (!$this->is_on_sale) {
            return 0;
        }
        return round((($this->price - $this->sale_price) / $this->price) * 100);
    }

    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->effective_price, 2);
    }

    public function getFormattedOriginalPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }

    public function getPrimaryImageAttribute()
    {
        if ($this->featured_image) {
            return $this->featured_image;
        }
        
        $primaryImage = $this->images()->where('is_primary', true)->first();
        return $primaryImage ? $primaryImage->image_path : null;
    }

    public function isDigital()
    {
        return $this->type === 'digital';
    }

    public function isPhysical()
    {
        return $this->type === 'physical';
    }

    public function canPurchase()
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->isPhysical() && $this->manage_stock && $this->stock_quantity <= 0) {
            return false;
        }

        return true;
    }

    public function decreaseStock($quantity)
    {
        if ($this->isPhysical() && $this->manage_stock) {
            $this->stock_quantity = max(0, $this->stock_quantity - $quantity);
            $this->in_stock = $this->stock_quantity > 0;
            $this->save();
        }
    }

    public function increaseStock($quantity)
    {
        if ($this->isPhysical() && $this->manage_stock) {
            $this->stock_quantity += $quantity;
            $this->in_stock = true;
            $this->save();
        }
    }
}
