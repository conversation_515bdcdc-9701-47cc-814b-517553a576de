import{N as R,V as T,r as p,o as U,d,e as n,f as v,i as e,u as s,m as O,l as c,I as S,A as C,x,t as m,j as D,n as g,v as M,K as B,O as A,g as N,P as H,F as q,p as W,a as G,J}from"./vendor-DwpQ5WHX.js";import{_ as f}from"./InputError.vue_vue_type_script_setup_true_lang-DSYw3Bxn.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const $={class:"min-h-screen bg-medroid-sage flex"},K={class:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-medroid-cream"},Q={class:"max-w-md w-full"},X=["href","disabled"],Z={key:0,class:"mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg"},ee={class:"text-sm text-orange-700"},te={key:1,class:"mb-4 text-center text-sm font-medium text-medroid-teal"},oe={key:2,class:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"},se={class:"flex items-start space-x-3"},re={class:"text-sm font-medium text-green-800 mb-1"},ae={key:3,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},ie={class:"flex items-start space-x-3"},de={class:"text-sm text-blue-700"},ne={key:0},le={key:1},ue={class:"flex space-x-6"},me={class:"flex items-center"},ce={class:"flex items-center"},ve={class:"flex items-center"},ge={key:2},fe=["readonly","placeholder"],pe={key:0,class:"mt-1 text-xs text-gray-500"},be={class:"relative"},xe=["type","autocomplete","placeholder"],he={key:0,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ye={key:1,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},we={key:3},ke={for:"referral_code",class:"block text-sm font-medium text-medroid-navy mb-2"},Ce=["required","placeholder"],Me={class:"mt-1 text-xs text-medroid-slate"},_e={key:4},Ve={class:"relative"},Se=["type"],Be={key:0,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},je={key:1,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie={class:"pt-4"},Ae=["disabled"],qe={class:"text-center pt-4"},ze={class:"text-sm text-medroid-slate"},Ee={key:5,class:"text-center"},Pe={class:"hidden lg:flex lg:w-1/2 bg-medroid-sage flex-col"},Fe={class:"flex-1 p-6 overflow-y-auto space-y-4 chat-container"},Le=R({__name:"Register",props:{canResetPassword:{type:Boolean,default:!1},status:{type:String,default:null},isLoginMode:{type:Boolean,default:!1},waitlistStatus:{type:Object,default:()=>({enabled:!1,messages:{}})},invitationToken:{type:String,default:null},invitationValid:{type:Boolean,default:!1},invitation:{type:Object,default:null}},setup(i){var I;const b=i,o=T({name:"",email:((I=b.invitation)==null?void 0:I.email)||"",password:"",password_confirmation:"",role:"patient",gender:"",date_of_birth:"",referral_code:"",invitation_token:b.invitationToken||""}),u=p(b.waitlistStatus||{enabled:!1,messages:{}}),z=async()=>{try{console.log("Fetching waitlist status from API...");const l=await G.get("/api/waitlist/status");u.value=l.data,console.log("Waitlist status fetched:",l.data),console.log("Waitlist enabled:",l.data.enabled)}catch(l){console.error("Error fetching waitlist status:",l)}},h=p(!1),y=p(!1),w=p(!1),r=p(b.isLoginMode),_=p([]),V=[{type:"user",text:"I have been feeling tired lately and having headaches. What could be the cause?"},{type:"bot",text:"I understand your concern. Fatigue and headaches can have various causes. Can you tell me more about when these symptoms started and if you've noticed any patterns?"},{type:"user",text:"It started about a week ago, mostly in the afternoons."},{type:"bot",text:"Based on your symptoms, this could be related to dehydration, stress, or sleep patterns. I recommend drinking more water, ensuring adequate sleep, and monitoring your symptoms. If they persist, please consult with a healthcare provider."},{type:"user",text:"Thank you! Should I be concerned about anything specific?"},{type:"bot",text:"Monitor for severe headaches, fever, or vision changes. These would require immediate medical attention. For now, focus on hydration and rest. Feel better soon!"}],j=()=>{_.value=[];let l=0;const t=()=>{var a;if(l<V.length){_.value.push(V[l]),l++;const k=((a=V[l-1])==null?void 0:a.type)==="bot"?2e3:1500;setTimeout(t,k)}else setTimeout(()=>{j()},3e3)};setTimeout(t,1e3)},E=()=>{w.value=!0;const l=r.value?"login":"register";o.post(route(l),{onFinish:()=>{r.value?o.reset("password"):o.reset("password","password_confirmation"),w.value=!1}})},P=()=>{h.value=!h.value},F=()=>{y.value=!y.value},L=()=>{r.value=!r.value,o.reset()};return U(()=>{j(),console.log("Component mounted. Props waitlistStatus:",b.waitlistStatus),z()}),(l,t)=>(n(),d(q,null,[v(s(O),{title:r.value?"Sign In":"Register"},null,8,["title"]),e("div",$,[e("div",K,[e("div",Q,[t[26]||(t[26]=e("div",{class:"text-left mb-8"},[e("h1",{class:"text-4xl font-bold text-medroid-navy mb-2"}," Your AI Doctor, "),e("h2",{class:"text-4xl font-bold text-medroid-navy mb-4"}," Always On Call "),e("p",{class:"text-medroid-slate mb-8"}," AI-powered healthcare that puts your well-being first. ")],-1)),e("a",{href:l.route("auth.google"),disabled:!r.value&&u.value.enabled&&!i.invitationValid,class:C(["w-full flex items-center justify-center px-4 py-3 border border-medroid-border rounded-lg shadow-sm text-sm font-medium transition-colors duration-200 mb-6 no-underline",!r.value&&u.value.enabled&&!i.invitationValid?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-medroid-navy bg-white hover:bg-medroid-sage","no-underline"])},[t[9]||(t[9]=S('<svg class="w-5 h-5 mr-3" viewBox="0 0 24 24" data-v-4c08f075><path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" data-v-4c08f075></path><path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" data-v-4c08f075></path><path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" data-v-4c08f075></path><path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" data-v-4c08f075></path></svg>',1)),x(" "+m(r.value?"Continue with Google":u.value.enabled&&!i.invitationValid?"Google Sign-up Disabled":"Continue with Google"),1)],10,X),!r.value&&u.value.enabled&&!i.invitationValid?(n(),d("div",Z,[e("p",ee,m(u.value.messages.google_signin_disabled),1)])):c("",!0),t[27]||(t[27]=S('<div class="relative mb-6" data-v-4c08f075><div class="absolute inset-0 flex items-center" data-v-4c08f075><div class="w-full border-t border-medroid-border" data-v-4c08f075></div></div><div class="relative flex justify-center text-sm" data-v-4c08f075><span class="px-2 bg-medroid-cream text-medroid-slate" data-v-4c08f075>OR</span></div></div>',1)),i.status?(n(),d("div",te,m(i.status),1)):c("",!0),!r.value&&i.invitationValid&&i.invitation?(n(),d("div",oe,[e("div",se,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-green-500 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[e("h3",re,"🎉 Welcome to "+m(i.invitation.club_info.club_name)+"!",1),t[10]||(t[10]=e("p",{class:"text-sm text-green-700"}," You've been invited to join Medroid. Complete your registration below. ",-1))])])])):!r.value&&u.value.enabled&&!i.invitationValid?(n(),d("div",ae,[e("div",ie,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-blue-500 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[t[12]||(t[12]=e("h3",{class:"text-sm font-medium text-blue-800 mb-1"},"Invitation Required",-1)),e("p",de,m(u.value.messages.signup_disabled),1),t[13]||(t[13]=e("p",{class:"text-sm text-blue-600 mt-2"},[e("a",{href:"/",class:"underline hover:text-blue-800"},"Join our waitlist"),x(" to get an invitation! ")],-1))])])])):c("",!0),e("form",{class:"space-y-5",onSubmit:D(E,["prevent"])},[r.value?c("",!0):(n(),d("div",ne,[g(e("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=a=>s(o).name=a),name:"name",type:"text",autocomplete:"name",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:"Enter your full name"},null,512),[[M,s(o).name]]),v(f,{class:"mt-2",message:s(o).errors.name},null,8,["message"])])),r.value?c("",!0):(n(),d("div",le,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-medroid-navy mb-3"},"Gender (Optional)",-1)),e("div",ue,[e("label",me,[g(e("input",{type:"radio","onUpdate:modelValue":t[1]||(t[1]=a=>s(o).gender=a),value:"male",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[B,s(o).gender]]),t[15]||(t[15]=e("span",{class:"ml-2 text-sm text-medroid-navy"},"Male",-1))]),e("label",ce,[g(e("input",{type:"radio","onUpdate:modelValue":t[2]||(t[2]=a=>s(o).gender=a),value:"female",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[B,s(o).gender]]),t[16]||(t[16]=e("span",{class:"ml-2 text-sm text-medroid-navy"},"Female",-1))]),e("label",ve,[g(e("input",{type:"radio","onUpdate:modelValue":t[3]||(t[3]=a=>s(o).gender=a),value:"other",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[B,s(o).gender]]),t[17]||(t[17]=e("span",{class:"ml-2 text-sm text-medroid-navy"},"Other",-1))])]),v(f,{class:"mt-2",message:s(o).errors.gender},null,8,["message"])])),r.value?c("",!0):(n(),d("div",ge,[t[19]||(t[19]=e("label",{for:"date_of_birth",class:"block text-sm font-medium text-medroid-navy mb-2"},"Date of Birth (Optional)",-1)),g(e("input",{id:"date_of_birth","onUpdate:modelValue":t[4]||(t[4]=a=>s(o).date_of_birth=a),name:"date_of_birth",type:"date",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy bg-white"},null,512),[[M,s(o).date_of_birth]]),v(f,{class:"mt-2",message:s(o).errors.date_of_birth},null,8,["message"]),t[20]||(t[20]=e("p",{class:"mt-1 text-xs text-medroid-slate"},"Helps us provide better healthcare recommendations",-1))])),e("div",null,[g(e("input",{id:"email","onUpdate:modelValue":t[5]||(t[5]=a=>s(o).email=a),name:"email",type:"email",autocomplete:"email",required:"",readonly:i.invitationValid&&i.invitation,class:C(["w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate",i.invitationValid&&i.invitation?"bg-gray-100":"bg-white"]),placeholder:r.value?"Enter your personal or work email":"Enter your email address"},null,10,fe),[[M,s(o).email]]),v(f,{class:"mt-2",message:s(o).errors.email},null,8,["message"]),i.invitationValid&&i.invitation?(n(),d("p",pe," Email is pre-filled from your invitation ")):c("",!0)]),e("div",null,[e("div",be,[g(e("input",{id:"password","onUpdate:modelValue":t[6]||(t[6]=a=>s(o).password=a),name:"password",type:h.value?"text":"password",autocomplete:r.value?"current-password":"new-password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:r.value?"Enter your password":"Create a strong password"},null,8,xe),[[A,s(o).password]]),e("button",{type:"button",onClick:P,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[h.value?(n(),d("svg",he,t[21]||(t[21]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),d("svg",ye,t[22]||(t[22]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),v(f,{class:"mt-2",message:s(o).errors.password},null,8,["message"])]),!r.value&&!i.invitationValid?(n(),d("div",we,[e("label",ke,m(u.value.enabled?"Invitation Code (Required)":"Referral Code (Optional)"),1),g(e("input",{id:"referral_code","onUpdate:modelValue":t[7]||(t[7]=a=>s(o).referral_code=a),name:"referral_code",type:"text",required:u.value.enabled,class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:u.value.enabled?"Enter your invitation code":"Enter referral code"},null,8,Ce),[[M,s(o).referral_code]]),v(f,{class:"mt-2",message:s(o).errors.referral_code},null,8,["message"]),e("p",Me,m(u.value.enabled?"An invitation code is required to create an account during our beta phase.":"Have a referral code? Enter it here to get bonus points!"),1)])):c("",!0),r.value?c("",!0):(n(),d("div",_e,[e("div",Ve,[g(e("input",{id:"password_confirmation","onUpdate:modelValue":t[8]||(t[8]=a=>s(o).password_confirmation=a),name:"password_confirmation",type:y.value?"text":"password",autocomplete:"new-password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:"Confirm your password"},null,8,Se),[[A,s(o).password_confirmation]]),e("button",{type:"button",onClick:F,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[y.value?(n(),d("svg",Be,t[23]||(t[23]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),d("svg",je,t[24]||(t[24]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),v(f,{class:"mt-2",message:s(o).errors.password_confirmation},null,8,["message"])])),e("div",Ie,[e("button",{type:"submit",disabled:s(o).processing||w.value,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"},m(s(o).processing||w.value?r.value?"Signing In...":"Creating Account...":r.value?"Sign In":"Sign Up"),9,Ae)]),e("div",qe,[e("p",ze,[x(m(r.value?"Don't have an account?":"Already have an account?")+" ",1),e("button",{type:"button",onClick:L,class:"font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline"},m(r.value?"Sign up":"Sign in here"),1)])]),r.value&&i.canResetPassword?(n(),d("div",Ee,[v(s(H),{href:l.route("password.request"),class:"text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200 underline"},{default:N(()=>t[25]||(t[25]=[x(" Forgot password? ")])),_:1},8,["href"])])):c("",!0)],32),t[28]||(t[28]=e("div",{class:"text-center mt-6"},[e("p",{class:"text-xs text-medroid-slate"},[x(" By continuing, you acknowledge Medroid's "),e("a",{href:"https://medroid.ai/privacy-policy/",target:"_blank",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Privacy Policy")])],-1))])]),e("div",Pe,[t[29]||(t[29]=e("div",{class:"bg-white p-6 border-b border-medroid-border"},[e("h3",{class:"text-xl font-semibold text-medroid-navy mb-2"},"Experience Medroid"),e("p",{class:"text-medroid-slate text-sm"},"Chat with our AI doctor for instant health insights")],-1)),e("div",Fe,[(n(!0),d(q,null,W(_.value,(a,k)=>(n(),d("div",{key:k,class:C([a.type==="user"?"flex justify-end":"flex justify-start","animate-fade-in-up"]),style:J({animationDelay:`${k*.1}s`})},[e("div",{class:C(["message-bubble max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm",a.type==="user"?"bg-medroid-orange text-white rounded-br-md shadow-lg":"bg-white text-medroid-navy border border-medroid-border rounded-bl-md shadow-sm hover:shadow-md"])},m(a.text),3)],6))),128))]),t[30]||(t[30]=S('<div class="p-6 bg-white border-t border-medroid-border" data-v-4c08f075><div class="flex items-center space-x-3" data-v-4c08f075><input type="text" placeholder="Type your health question here..." class="flex-1 px-4 py-3 border border-medroid-border rounded-full focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange text-sm text-medroid-navy placeholder-medroid-slate" disabled data-v-4c08f075><button class="bg-medroid-orange hover:bg-medroid-orange/90 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50" disabled data-v-4c08f075><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4c08f075><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" data-v-4c08f075></path></svg></button></div><div class="mt-4 text-center" data-v-4c08f075><a href="#" class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200" data-v-4c08f075> Learn more about Medroid → </a></div></div>',1))])])],64))}}),Oe=Y(Le,[["__scopeId","data-v-4c08f075"]]);export{Oe as default};
