<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link, router } from '@inertiajs/vue3'
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'

const breadcrumbs = [
    { title: 'Shop', href: '/shop' },
    { title: 'Shopping Cart', href: '/shop/cart' },
]

// Reactive data
const cartItems = ref([])
const loading = ref(false)
const updating = ref({})

// Computed properties
const cartTotal = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.total_price, 0)
})

const formattedTotal = computed(() => {
    return '$' + cartTotal.value.toFixed(2)
})

const cartCount = computed(() => {
    return cartItems.value.reduce((count, item) => count + item.quantity, 0)
})

// Methods
const loadCart = async () => {
    loading.value = true
    try {
        const response = await axios.get('/shop/cart')
        cartItems.value = response.data.cart_items || []
    } catch (error) {
        console.error('Error loading cart:', error)
        cartItems.value = []
    } finally {
        loading.value = false
    }
}

const updateQuantity = async (item, newQuantity) => {
    if (newQuantity < 0) return
    
    updating.value[item.product_id] = true
    try {
        const response = await axios.put(`/shop/cart/${item.product_id}`, {
            quantity: newQuantity
        })
        
        if (response.data.success) {
            if (newQuantity === 0) {
                cartItems.value = cartItems.value.filter(cartItem => cartItem.product_id !== item.product_id)
            } else {
                const index = cartItems.value.findIndex(cartItem => cartItem.product_id === item.product_id)
                if (index !== -1) {
                    cartItems.value[index].quantity = newQuantity
                    cartItems.value[index].total_price = newQuantity * cartItems.value[index].price
                }
            }
        } else {
            alert(response.data.message || 'Failed to update cart')
        }
    } catch (error) {
        console.error('Error updating cart:', error)
        alert('Failed to update cart')
    } finally {
        updating.value[item.product_id] = false
    }
}

const removeItem = async (item) => {
    if (!confirm('Remove this item from cart?')) return
    
    updating.value[item.product_id] = true
    try {
        const response = await axios.delete(`/shop/cart/${item.product_id}`)
        
        if (response.data.success) {
            cartItems.value = cartItems.value.filter(cartItem => cartItem.product_id !== item.product_id)
        } else {
            alert(response.data.message || 'Failed to remove item')
        }
    } catch (error) {
        console.error('Error removing item:', error)
        alert('Failed to remove item')
    } finally {
        updating.value[item.product_id] = false
    }
}

const clearCart = async () => {
    if (!confirm('Clear all items from cart?')) return
    
    loading.value = true
    try {
        const response = await axios.delete('/shop/cart')
        
        if (response.data.success) {
            cartItems.value = []
        } else {
            alert(response.data.message || 'Failed to clear cart')
        }
    } catch (error) {
        console.error('Error clearing cart:', error)
        alert('Failed to clear cart')
    } finally {
        loading.value = false
    }
}

const proceedToCheckout = () => {
    if (cartItems.value.length === 0) {
        alert('Your cart is empty')
        return
    }
    router.visit('/shop/checkout')
}

// Initialize
onMounted(() => {
    loadCart()
})
</script>

<template>
    <Head title="Shopping Cart - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Shopping Cart</h1>
                                <p class="text-gray-600 mt-1">{{ cartCount }} {{ cartCount === 1 ? 'item' : 'items' }} in your cart</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <Link 
                                    href="/shop" 
                                    class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Continue Shopping
                                </Link>
                                <button
                                    v-if="cartItems.length > 0"
                                    @click="clearCart"
                                    class="inline-flex items-center px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                                >
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Clear Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-12 text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">Loading cart...</p>
                    </div>
                </div>

                <!-- Empty Cart -->
                <div v-else-if="cartItems.length === 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-12 text-center">
                        <div class="text-6xl mb-4">🛒</div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
                        <p class="text-gray-600 mb-6">Add some products to get started!</p>
                        <Link 
                            href="/shop" 
                            class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            Start Shopping
                        </Link>
                    </div>
                </div>

                <!-- Cart Items -->
                <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Cart Items List -->
                    <div class="lg:col-span-2">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Cart Items</h2>
                                <div class="space-y-4">
                                    <div
                                        v-for="item in cartItems"
                                        :key="item.id"
                                        class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"
                                    >
                                        <!-- Product Image -->
                                        <div class="flex-shrink-0">
                                            <img
                                                :src="item.product.primary_image || '/images/placeholder-product.jpg'"
                                                :alt="item.product.name"
                                                class="h-20 w-20 object-cover rounded-lg"
                                            />
                                        </div>

                                        <!-- Product Info -->
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-lg font-semibold text-gray-900">{{ item.product.name }}</h3>
                                            <p class="text-gray-600 text-sm">{{ item.product.short_description }}</p>
                                            <div class="flex items-center mt-2">
                                                <span class="text-lg font-bold text-blue-600">${{ item.price.toFixed(2) }}</span>
                                                <span v-if="item.product.type === 'digital'" class="ml-2 bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded">
                                                    Digital
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Quantity Controls -->
                                        <div class="flex items-center space-x-2">
                                            <button
                                                @click="updateQuantity(item, item.quantity - 1)"
                                                :disabled="updating[item.product_id] || item.quantity <= 1"
                                                class="w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                            >
                                                -
                                            </button>
                                            <span class="w-12 text-center font-medium">{{ item.quantity }}</span>
                                            <button
                                                @click="updateQuantity(item, item.quantity + 1)"
                                                :disabled="updating[item.product_id]"
                                                class="w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                            >
                                                +
                                            </button>
                                        </div>

                                        <!-- Total Price -->
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">${{ item.total_price.toFixed(2) }}</div>
                                            <button
                                                @click="removeItem(item)"
                                                :disabled="updating[item.product_id]"
                                                class="text-red-600 hover:text-red-800 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                                
                                <div class="space-y-3 mb-6">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="font-medium">{{ formattedTotal }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Shipping</span>
                                        <span class="font-medium">Calculated at checkout</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tax</span>
                                        <span class="font-medium">Calculated at checkout</span>
                                    </div>
                                    <div class="border-t pt-3">
                                        <div class="flex justify-between">
                                            <span class="text-lg font-semibold">Total</span>
                                            <span class="text-lg font-bold text-blue-600">{{ formattedTotal }}</span>
                                        </div>
                                    </div>
                                </div>

                                <button
                                    @click="proceedToCheckout"
                                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                                >
                                    Proceed to Checkout
                                </button>

                                <div class="mt-4 text-center">
                                    <p class="text-sm text-gray-500">
                                        Secure checkout powered by Stripe
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
